package cn.gzsf.javawebspringboot.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码加密工具类
 * 使用BCrypt算法进行密码加密和验证
 */
@Component
public class PasswordEncoder {

    private final BCryptPasswordEncoder bCryptPasswordEncoder;

    public PasswordEncoder() {
        // 使用强度为12的BCrypt加密（默认是10，12更安全但稍慢）
        this.bCryptPasswordEncoder = new BCryptPasswordEncoder(12);
    }

    /**
     * 加密密码
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public String encode(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        // 密码长度验证
        if (rawPassword.length() < 6) {
            throw new IllegalArgumentException("密码长度不能少于6位");
        }
        
        if (rawPassword.length() > 100) {
            throw new IllegalArgumentException("密码长度不能超过100位");
        }
        
        String encodedPassword = bCryptPasswordEncoder.encode(rawPassword);
        System.out.println("🔐 密码加密成功，原始长度: " + rawPassword.length() + ", 加密后长度: " + encodedPassword.length());
        return encodedPassword;
    }

    /**
     * 验证密码
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            System.out.println("❌ 密码验证失败: 密码或加密密码为空");
            return false;
        }
        
        try {
            boolean matches = bCryptPasswordEncoder.matches(rawPassword, encodedPassword);
            System.out.println(matches ? "✅ 密码验证成功" : "❌ 密码验证失败");
            return matches;
        } catch (Exception e) {
            System.out.println("❌ 密码验证异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查密码强度
     * @param password 密码
     * @return 强度等级 (1-5, 5最强)
     */
    public int checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return 0;
        }
        
        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) score++;
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) score++;
        
        // 包含数字
        if (password.matches(".*[0-9].*")) score++;
        
        // 包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score++;
        
        // 最高5分
        return Math.min(5, score);
    }

    /**
     * 获取密码强度描述
     * @param password 密码
     * @return 强度描述
     */
    public String getPasswordStrengthDescription(String password) {
        int strength = checkPasswordStrength(password);
        switch (strength) {
            case 0:
            case 1:
                return "弱";
            case 2:
            case 3:
                return "中等";
            case 4:
                return "强";
            case 5:
                return "很强";
            default:
                return "未知";
        }
    }

    /**
     * 验证密码格式
     * @param password 密码
     * @return 验证结果和错误信息
     */
    public ValidationResult validatePasswordFormat(String password) {
        if (password == null || password.trim().isEmpty()) {
            return new ValidationResult(false, "密码不能为空");
        }
        
        if (password.length() < 6) {
            return new ValidationResult(false, "密码长度不能少于6位");
        }
        
        if (password.length() > 100) {
            return new ValidationResult(false, "密码长度不能超过100位");
        }
        
        // 检查是否包含空格
        if (password.contains(" ")) {
            return new ValidationResult(false, "密码不能包含空格");
        }
        
        // 建议包含数字和字母
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        boolean hasDigit = password.matches(".*[0-9].*");
        
        if (!hasLetter && !hasDigit) {
            return new ValidationResult(false, "密码应包含字母或数字");
        }
        
        return new ValidationResult(true, "密码格式正确");
    }

    /**
     * 密码验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}

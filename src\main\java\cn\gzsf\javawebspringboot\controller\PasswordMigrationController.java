package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.util.PasswordEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 密码迁移控制器
 * 用于将现有的明文密码迁移为加密密码
 */
@RestController
@RequestMapping("/admin/password-migration")
public class PasswordMigrationController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 检查需要迁移的密码数量
     */
    @GetMapping("/check")
    public Map<String, Object> checkMigrationNeeded() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询所有用户的密码，检查哪些是明文密码
            String sql = "SELECT id, user_id, username, password FROM user";
            List<Map<String, Object>> users = jdbcTemplate.queryForList(sql);
            
            int totalUsers = users.size();
            int needMigration = 0;
            int alreadyEncrypted = 0;
            
            for (Map<String, Object> user : users) {
                String password = (String) user.get("password");
                if (password != null) {
                    // BCrypt密码通常以$2a$、$2b$、$2y$开头，长度为60
                    if (password.startsWith("$2") && password.length() == 60) {
                        alreadyEncrypted++;
                    } else {
                        needMigration++;
                    }
                }
            }
            
            result.put("success", true);
            result.put("totalUsers", totalUsers);
            result.put("needMigration", needMigration);
            result.put("alreadyEncrypted", alreadyEncrypted);
            result.put("message", "检查完成");
            
            System.out.println("🔍 密码迁移检查: 总用户数=" + totalUsers + 
                             ", 需要迁移=" + needMigration + 
                             ", 已加密=" + alreadyEncrypted);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 执行密码迁移
     */
    @PostMapping("/migrate")
    public Map<String, Object> migratePasswords() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询所有需要迁移的用户
            String sql = "SELECT id, user_id, username, password FROM user";
            List<Map<String, Object>> users = jdbcTemplate.queryForList(sql);
            
            int migratedCount = 0;
            int skippedCount = 0;
            int errorCount = 0;
            
            for (Map<String, Object> user : users) {
                Long id = (Long) user.get("id");
                String userId = (String) user.get("user_id");
                String password = (String) user.get("password");
                
                if (password == null || password.trim().isEmpty()) {
                    skippedCount++;
                    continue;
                }
                
                // 检查是否已经是加密密码
                if (password.startsWith("$2") && password.length() == 60) {
                    skippedCount++;
                    continue;
                }
                
                try {
                    // 加密密码
                    String encodedPassword = passwordEncoder.encode(password);
                    
                    // 更新数据库
                    String updateSql = "UPDATE user SET password = ? WHERE id = ?";
                    int updated = jdbcTemplate.update(updateSql, encodedPassword, id);
                    
                    if (updated > 0) {
                        migratedCount++;
                        System.out.println("✅ 用户密码迁移成功: " + userId);
                    } else {
                        errorCount++;
                        System.out.println("❌ 用户密码迁移失败: " + userId);
                    }
                    
                } catch (Exception e) {
                    errorCount++;
                    System.out.println("❌ 用户密码加密失败: " + userId + ", 错误: " + e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("migratedCount", migratedCount);
            result.put("skippedCount", skippedCount);
            result.put("errorCount", errorCount);
            result.put("message", "密码迁移完成");
            
            System.out.println("🔐 密码迁移完成: 迁移=" + migratedCount + 
                             ", 跳过=" + skippedCount + 
                             ", 错误=" + errorCount);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "密码迁移失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 重置特定用户密码
     */
    @PostMapping("/reset-password")
    public Map<String, Object> resetUserPassword(@RequestBody Map<String, String> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String userId = requestData.get("userId");
            String newPassword = requestData.get("newPassword");
            
            if (userId == null || newPassword == null) {
                result.put("success", false);
                result.put("message", "用户ID和新密码不能为空");
                return result;
            }
            
            // 验证密码格式
            PasswordEncoder.ValidationResult validation = passwordEncoder.validatePasswordFormat(newPassword);
            if (!validation.isValid()) {
                result.put("success", false);
                result.put("message", validation.getMessage());
                return result;
            }
            
            // 加密新密码
            String encodedPassword = passwordEncoder.encode(newPassword);
            
            // 更新数据库
            String updateSql = "UPDATE user SET password = ? WHERE user_id = ?";
            int updated = jdbcTemplate.update(updateSql, encodedPassword, userId);
            
            if (updated > 0) {
                result.put("success", true);
                result.put("message", "密码重置成功");
                System.out.println("🔐 管理员重置用户密码: " + userId);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在或重置失败");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "密码重置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 验证密码加密是否正常工作
     */
    @PostMapping("/test-encryption")
    public Map<String, Object> testEncryption(@RequestBody Map<String, String> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testPassword = requestData.get("password");
            if (testPassword == null) {
                testPassword = "test123456";
            }
            
            // 加密测试
            String encoded = passwordEncoder.encode(testPassword);
            
            // 验证测试
            boolean matches = passwordEncoder.matches(testPassword, encoded);
            
            // 密码强度测试
            int strength = passwordEncoder.checkPasswordStrength(testPassword);
            String strengthDesc = passwordEncoder.getPasswordStrengthDescription(testPassword);
            
            result.put("success", true);
            result.put("originalPassword", testPassword);
            result.put("encodedPassword", encoded);
            result.put("verificationResult", matches);
            result.put("passwordStrength", strength);
            result.put("strengthDescription", strengthDesc);
            result.put("message", "加密测试完成");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "加密测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}

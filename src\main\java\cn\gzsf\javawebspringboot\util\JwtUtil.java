package cn.gzsf.javawebspringboot.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 用于生成、验证和解析JWT令牌
 */
@Component
public class JwtUtil {

    // JWT密钥 - 在生产环境中应该从配置文件读取
    private static final String SECRET_KEY = "xirangji_beauty_website_secret_key_2024_very_long_and_secure_key_for_jwt_token_generation";
    
    // Token过期时间（7天）
    private static final long EXPIRATION_TIME = 7 * 24 * 60 * 60 * 1000; // 7天
    
    // 短期Token过期时间（2小时）
    private static final long SHORT_EXPIRATION_TIME = 2 * 60 * 60 * 1000; // 2小时

    private final SecretKey key;

    public JwtUtil() {
        // 使用HMAC-SHA算法生成密钥
        this.key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes());
    }

    /**
     * 生成JWT令牌
     * @param userId 用户ID
     * @param phone 用户手机号
     * @param username 用户名
     * @param longTerm 是否长期有效
     * @return JWT令牌
     */
    public String generateToken(String userId, String phone, String username, boolean longTerm) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("phone", phone);
        claims.put("username", username);
        claims.put("type", longTerm ? "long_term" : "short_term");
        
        long expiration = longTerm ? EXPIRATION_TIME : SHORT_EXPIRATION_TIME;
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(userId)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成默认JWT令牌（长期有效）
     */
    public String generateToken(String userId, String phone, String username) {
        return generateToken(userId, phone, username, true);
    }

    /**
     * 从令牌中提取用户ID
     */
    public String extractUserId(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * 从令牌中提取手机号
     */
    public String extractPhone(String token) {
        return extractClaim(token, claims -> claims.get("phone", String.class));
    }

    /**
     * 从令牌中提取用户名
     */
    public String extractUsername(String token) {
        return extractClaim(token, claims -> claims.get("username", String.class));
    }

    /**
     * 从令牌中提取过期时间
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * 提取令牌中的特定声明
     */
    public <T> T extractClaim(String token, ClaimsResolver<T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.resolve(claims);
    }

    /**
     * 提取令牌中的所有声明
     */
    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            System.out.println("⚠️ JWT令牌已过期: " + e.getMessage());
            throw new RuntimeException("令牌已过期，请重新登录");
        } catch (UnsupportedJwtException e) {
            System.out.println("❌ 不支持的JWT令牌: " + e.getMessage());
            throw new RuntimeException("无效的令牌格式");
        } catch (MalformedJwtException e) {
            System.out.println("❌ JWT令牌格式错误: " + e.getMessage());
            throw new RuntimeException("令牌格式错误");
        } catch (SecurityException e) {
            System.out.println("❌ JWT令牌签名验证失败: " + e.getMessage());
            throw new RuntimeException("令牌签名验证失败");
        } catch (IllegalArgumentException e) {
            System.out.println("❌ JWT令牌为空: " + e.getMessage());
            throw new RuntimeException("令牌不能为空");
        }
    }

    /**
     * 检查令牌是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            return true; // 如果解析失败，认为已过期
        }
    }

    /**
     * 验证令牌
     */
    public Boolean validateToken(String token, String userId) {
        try {
            final String extractedUserId = extractUserId(token);
            return (extractedUserId.equals(userId) && !isTokenExpired(token));
        } catch (Exception e) {
            System.out.println("❌ 令牌验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证令牌（不需要用户ID）
     */
    public Boolean validateToken(String token) {
        try {
            extractAllClaims(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            System.out.println("❌ 令牌验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 刷新令牌
     */
    public String refreshToken(String token) {
        try {
            Claims claims = extractAllClaims(token);
            String userId = claims.getSubject();
            String phone = claims.get("phone", String.class);
            String username = claims.get("username", String.class);
            String type = claims.get("type", String.class);
            
            boolean longTerm = "long_term".equals(type);
            return generateToken(userId, phone, username, longTerm);
        } catch (Exception e) {
            throw new RuntimeException("令牌刷新失败: " + e.getMessage());
        }
    }

    /**
     * 获取令牌剩余有效时间（秒）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            long remaining = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remaining / 1000); // 返回秒数
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 声明解析器接口
     */
    @FunctionalInterface
    public interface ClaimsResolver<T> {
        T resolve(Claims claims);
    }
}

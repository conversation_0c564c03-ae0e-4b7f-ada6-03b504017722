<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>息壤集后台管理系统</title>
    <!-- 使用一个简单的emoji作为favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌙</text></svg>">
    <!-- 引入外部资源 - 使用多个CDN备用 -->
    <script>
        // CDN加载失败时的备用方案
        function loadScript(src, fallbackSrc) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => {
                    console.warn('主CDN加载失败，尝试备用CDN:', src);
                    if (fallbackSrc) {
                        const fallbackScript = document.createElement('script');
                        fallbackScript.src = fallbackSrc;
                        fallbackScript.onload = resolve;
                        fallbackScript.onerror = reject;
                        document.head.appendChild(fallbackScript);
                    } else {
                        reject();
                    }
                };
                document.head.appendChild(script);
            });
        }

        function loadCSS(href, fallbackHref) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onerror = () => {
                console.warn('主CDN CSS加载失败，尝试备用CDN:', href);
                if (fallbackHref) {
                    const fallbackLink = document.createElement('link');
                    fallbackLink.rel = 'stylesheet';
                    fallbackLink.href = fallbackHref;
                    document.head.appendChild(fallbackLink);
                }
            };
            document.head.appendChild(link);
        }

        // 加载Element UI CSS
        loadCSS(
            'https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css',
            'https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.14/theme-chalk/index.css'
        );
    </script>

    <!-- 直接内联Vue.js以避免CDN问题 -->
    <script>
        // 如果CDN加载失败，使用内联的Vue.js精简版
        window.vueLoaded = false;
    </script>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.min.js" onload="window.vueLoaded = true" onerror="console.warn('Vue CDN加载失败')"></script>
    <script src="https://unpkg.com/axios@1.6.0/dist/axios.min.js" onerror="console.warn('Axios CDN加载失败')"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js" onerror="console.warn('Element UI CDN加载失败')"></script>
    <!-- 使用简化的图标字体 -->
    <style>
        /* 简化的图标字体 - 避免外部字体加载 */
        .fa:before, [class^="fa-"]:before, [class*=" fa-"]:before {
            font-family: FontAwesome;
            font-weight: normal;
            font-style: normal;
            text-decoration: inherit;
            -webkit-font-smoothing: antialiased;
        }
        .fa-tachometer:before { content: "📊"; }
        .fa-users:before { content: "👥"; }
        .fa-shopping-bag:before { content: "🛍️"; }
        .fa-tags:before { content: "🏷️"; }
        .fa-shopping-cart:before { content: "🛒"; }
        .fa-bar-chart:before { content: "📈"; }
        .fa-cog:before { content: "⚙️"; }
        .fa-makeup:before { content: "🌙"; }
        .fa-search:before { content: "🔍"; }
        .fa-bell-o:before { content: "🔔"; }
        .fa-user-o:before { content: "👤"; }
        .fa-sign-out:before { content: "🚪"; }
        .fa-caret-down:before { content: "▼"; }
        .fa-arrow-up:before { content: "↑"; }
        .fa-arrow-down:before { content: "↓"; }
        .fa-line-chart:before { content: "📊"; }
    </style>
    <!-- 简化的Tailwind CSS配置 -->
    <style>
        /* 基础Tailwind样式 - 内联版本 */
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .flex-1 { flex: 1 1 0%; }
        .items-center { align-items: center; }
        .justify-between { justify-content: space-between; }
        .space-x-2 > * + * { margin-left: 0.5rem; }
        .space-x-3 > * + * { margin-left: 0.75rem; }
        .space-x-4 > * + * { margin-left: 1rem; }
        .space-y-1 > * + * { margin-top: 0.25rem; }
        .p-2 { padding: 0.5rem; }
        .p-3 { padding: 0.75rem; }
        .p-4 { padding: 1rem; }
        .p-5 { padding: 1.25rem; }
        .p-6 { padding: 1.5rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
        .m-4 { margin: 1rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .w-8 { width: 2rem; }
        .w-10 { width: 2.5rem; }
        .w-12 { width: 3rem; }
        .w-48 { width: 12rem; }
        .w-64 { width: 16rem; }
        .w-full { width: 100%; }
        .h-8 { height: 2rem; }
        .h-10 { height: 2.5rem; }
        .h-12 { height: 3rem; }
        .h-16 { height: 4rem; }
        .h-full { height: 100%; }
        .min-h-screen { min-height: 100vh; }
        .bg-white { background-color: #ffffff; }
        .bg-neutral-100 { background-color: #f5f5f5; }
        .bg-neutral-200 { background-color: #e5e5e5; }
        .text-primary { color: #4CAF50; }
        .text-neutral-600 { color: #525252; }
        .text-neutral-800 { color: #262626; }
        .text-xl { font-size: 1.25rem; }
        .text-2xl { font-size: 1.5rem; }
        .text-lg { font-size: 1.125rem; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .font-bold { font-weight: 700; }
        .font-medium { font-weight: 500; }
        .font-semibold { font-weight: 600; }
        .rounded { border-radius: 0.25rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-xl { border-radius: 0.75rem; }
        .rounded-full { border-radius: 9999px; }
        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .border { border-width: 1px; }
        .border-2 { border-width: 2px; }
        .border-l-4 { border-left-width: 4px; }
        .border-primary { border-color: #4CAF50; }
        .border-secondary { border-color: #81C784; }
        .border-accent { border-color: #66BB6A; }
        .border-green-500 { border-color: #10b981; }
        .border-neutral-200 { border-color: #e5e5e5; }
        .border-neutral-300 { border-color: #d4d4d4; }
        .object-cover { object-fit: cover; }
        .overflow-hidden { overflow: hidden; }
        .overflow-auto { overflow: auto; }
        .hidden { display: none; }
        .block { display: block; }
        .grid { display: grid; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        .grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }
        .cursor-pointer { cursor: pointer; }
        .transition-all { transition: all 0.3s ease; }
        .hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .hover\:bg-neutral-200:hover { background-color: #e5e5e5; }
        .container { width: 100%; margin-left: auto; margin-right: auto; max-width: 1200px; }
        .relative { position: relative; }
        .absolute { position: absolute; }
        .top-1 { top: 0.25rem; }
        .right-0 { right: 0; }
        .right-1 { right: 0.25rem; }
        .z-10 { z-index: 10; }
        .z-50 { z-index: 50; }
        @media (min-width: 768px) {
            .md\:flex { display: flex; }
            .md\:block { display: block; }
            .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
            .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
            .md\:p-6 { padding: 1.5rem; }
            .md\:inline { display: inline; }
        }
        @media (min-width: 1024px) {
            .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
        }
    </style>
    <style>
        /* 自定义样式和工具类 */
        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .sidebar-item:hover {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1));
            color: #4CAF50;
            border-left: 3px solid #81C784;
            transform: translateX(5px);
        }
        .dropdown-content {
            position: absolute;
            right: 0;
            margin-top: 0.5rem;
            width: 12rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 0.25rem 0;
            z-index: 50;
            border: 1px solid #e5e5e5;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            transform: scale(0.95);
            transform-origin: top right;
        }
        .dropdown-content.show {
            opacity: 1;
            visibility: visible;
            transform: scale(1);
        }
        .dropdown-item {
            display: block;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: #525252;
            text-decoration: none;
        }
        .dropdown-item:hover {
            background-color: #f5f5f5;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .el-table th {
            background-color: #f5f5f5 !important;
        }
        .el-dialog__header {
            background-color: #f8f8f8;
            border-bottom: 1px solid #eee;
        }
        .el-card__header {
            font-weight: 600;
            color: #333;
        }
        .transition-all {
            transition: all 0.3s ease;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        /* 添加放大效果的样式 */
        .panel-full-width {
            grid-column: 1 / -1;
        }
        /* 侧边栏样式 */
        .sidebar-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
        }
        .sidebar-footer {
            border-top: 1px solid #e0e0e0;
            padding: 1rem;
        }

        /* 页面切换动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 侧边栏激活状态样式 */
        .sidebar-item.active {
            background: linear-gradient(135deg, #4CAF50, #81C784, #A5D6A7);
            color: white;
            font-weight: 600;
            border-left: 4px solid #2E7D32;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .sidebar-item.active i {
            color: white;
        }

        /* 仪表盘卡片边框样式 */
        .dashboard-card-primary {
            position: relative;
        }
        .dashboard-card-secondary {
            position: relative;
        }
        .dashboard-card-accent {
            position: relative;
        }
        .dashboard-card-green {
            position: relative;
        }
        .dashboard-card-accent {
            border-left: 4px solid #E91E63 !important;
        }
        .dashboard-card-green {
            border-left: 4px solid #10b981 !important;
        }

        /* 修复导航栏布局 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* 修复搜索框样式 */
        .search-input {
            width: 100%;
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid #d4d4d4;
            border-radius: 0.5rem;
            outline: none;
            transition: all 0.3s ease;
        }
        .search-input:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
            background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(232, 245, 233, 0.3));
        }

        /* 修复按钮样式 */
        .nav-button {
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
        }
        .nav-button:hover {
            background-color: #f5f5f5;
        }

        /* 修复用户菜单 */
        .user-menu {
            position: relative;
        }

        /* 修复响应式 */
        @media (max-width: 768px) {
            .search-container {
                display: none;
            }
            .sidebar {
                display: none;
            }
        }

        /* 产品对话框样式优化 */
        .product-dialog .el-dialog {
            max-height: 95vh;
            overflow-y: auto;
        }

        .product-dialog .el-dialog__body {
            max-height: 80vh;
            overflow-y: auto;
            padding: 20px;
        }

        /* 图片卡片样式 */
        .new-image-card, .edit-image-card {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .new-image-card:hover, .edit-image-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 图片卡片悬停效果 */
        .image-card-hover {
            border-color: #409eff !important;
            transform: translateY(-2px) !important;
        }

        .image-delete-hover {
            transform: scale(1.2) rotate(180deg) !important;
            background: #dc2626 !important;
        }

        .image-primary-hover {
            transform: scale(1.2) rotate(360deg) !important;
            background: #409eff !important;
        }

        /* 轮播图上传样式 */
        .carousel-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            width: 300px;
            height: 180px;
        }

        .carousel-uploader .el-upload:hover {
            border-color: #409EFF;
        }

        .carousel-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 300px;
            height: 180px;
            line-height: 180px;
            text-align: center;
        }

        .carousel-image {
            width: 300px;
            height: 180px;
            display: block;
            object-fit: cover;
        }

        /* 用户菜单悬停效果 */
        .user-menu-hover {
            background-color: #f5f5f5 !important;
        }

        /* 用户详情对话框样式 */
        .user-detail-dialog .el-dialog__body {
            padding: 0;
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
        }

        .user-detail-dialog .el-dialog__header {
            display: none;
        }

        .user-detail-content {
            max-height: 75vh;
            overflow-y: auto;
        }

        .custom-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 30px 35px;
            margin: -20px -20px 0 -20px;
            position: relative;
            overflow: hidden;
        }

        .custom-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header-avatar img {
            border: 4px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .header-avatar img:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
        }

        .user-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 35px;
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
        }

        .info-card {
            background: linear-gradient(135deg, #ffffff 0%, #f0fdfa 100%);
            border-radius: 20px;
            border: 2px solid #e6fffa;
            overflow: hidden;
            transition: all 0.4s ease;
            position: relative;
            box-shadow: 0 4px 20px rgba(6, 182, 212, 0.08);
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .info-card:hover {
            border-color: #06b6d4;
            box-shadow: 0 15px 40px rgba(6, 182, 212, 0.15);
            transform: translateY(-5px);
        }

        .info-card:hover::before {
            transform: scaleX(1);
        }

        .card-header {
            background: linear-gradient(135deg, #e6fffa 0%, #ccfbf1 100%);
            padding: 22px 28px;
            border-bottom: 1px solid rgba(6, 182, 212, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #06b6d4, #0891b2, #06b6d4);
            opacity: 0.3;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            color: #0f766e;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-header h3::before {
            content: '';
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        .address-count {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .card-content {
            padding: 25px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item .label {
            color: #666;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .info-item .value {
            color: #333;
            font-weight: 600;
        }

        .info-item .value.signature {
            font-style: italic;
            color: #888;
            max-width: 200px;
            text-align: right;
        }

        .info-item .value.friends-count {
            color: #ff6b9d;
            font-weight: bold;
        }

        .addresses-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .address-item {
            margin-bottom: 18px;
            padding: 18px;
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border-radius: 15px;
            border-left: 5px solid #06b6d4;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .address-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent 0%, rgba(6, 182, 212, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .address-item:hover {
            background: linear-gradient(135deg, #cffafe 0%, #a7f3d0 100%);
            transform: translateX(8px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15);
        }

        .address-item:hover::before {
            opacity: 1;
        }

        .address-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .receiver-name {
            color: #333;
            font-weight: 600;
            font-size: 1rem;
        }

        .receiver-phone {
            color: #666;
            font-size: 0.9rem;
        }

        .address-detail {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .cart-stats, .orders-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 18px;
            padding: 24px;
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border-radius: 16px;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(6, 182, 212, 0.1);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #06b6d4, #0891b2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            background: linear-gradient(135deg, #cffafe 0%, #a7f3d0 100%);
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(6, 182, 212, 0.2);
        }

        .stat-item:hover::before {
            transform: scaleX(1);
        }

        .stat-item.total-orders {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .stat-item.total-spent {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .stat-icon {
            width: 55px;
            height: 55px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            border: 3px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
        }

        .stat-icon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-item:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-item:hover .stat-icon::before {
            opacity: 1;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-style: italic;
        }

        .no-data i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
            color: #ddd;
        }

        @media (max-width: 768px) {
            .user-content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .cart-stats, .orders-stats {
                grid-template-columns: 1fr;
            }
        }

        /* 产品图片管理对话框样式 */
        .product-image-dialog .el-dialog__body {
            padding: 30px;
            max-height: 75vh;
            overflow-y: auto;
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
        }

        .product-image-dialog .custom-header {
            background: linear-gradient(135deg, #e6fffa 0%, #ccfbf1 100%);
            margin: -30px -30px 0 -30px;
            padding: 28px 32px;
            border-radius: 12px 12px 0 0;
            border-bottom: 2px solid rgba(6, 182, 212, 0.1);
        }

        .product-image-dialog .header-icon {
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .product-image-dialog .upload-area {
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border: 3px dashed #5eead4;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .product-image-dialog .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
            transition: left 0.5s;
        }

        .product-image-dialog .upload-area:hover {
            border-color: #06b6d4;
            background: linear-gradient(135deg, #cffafe 0%, #a7f3d0 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(6, 182, 212, 0.2);
        }

        .product-image-dialog .upload-area:hover::before {
            left: 100%;
        }

        .product-image-dialog .upload-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .product-image-dialog .image-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: linear-gradient(135deg, #ffffff 0%, #f0fdfa 100%);
            position: relative;
            overflow: hidden;
        }

        .product-image-dialog .image-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(6, 182, 212, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-image-dialog .image-card:hover {
            border-color: #06b6d4;
            transform: translateY(-6px);
            box-shadow: 0 15px 40px rgba(6, 182, 212, 0.15);
        }

        .product-image-dialog .image-card:hover::before {
            opacity: 1;
        }

        .product-image-dialog .primary-badge {
            animation: glow 2s ease-in-out infinite alternate;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
        }

        @keyframes glow {
            from { box-shadow: 0 0 8px rgba(6, 182, 212, 0.6); }
            to { box-shadow: 0 0 20px rgba(6, 182, 212, 0.9); }
        }

        .product-image-dialog .image-number {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .product-image-dialog .no-images {
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border: 2px dashed #5eead4;
        }

        .product-image-dialog .no-images-icon {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .product-image-dialog .section-header h3 {
            position: relative;
        }

        .product-image-dialog .section-header h3::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 60px;
            height: 4px;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            border-radius: 2px;
        }

        .product-image-dialog .dialog-footer {
            background: linear-gradient(135deg, #e6fffa 0%, #ccfbf1 100%);
            margin: 0 -30px -30px -30px;
            padding: 24px 32px;
            border-radius: 0 0 12px 12px;
            border-top: 2px solid rgba(6, 182, 212, 0.1);
        }

        .product-image-dialog .action-buttons .el-button {
            min-width: 100px;
            font-weight: 500;
        }

        /* 产品详情对话框图片样式优化 */
        .product-detail-dialog .image-gallery-container {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            padding: 15px;
            border: 1px solid #e2e8f0;
        }

        .product-detail-dialog .gallery-header {
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 8px;
        }

        .product-detail-dialog .gallery-grid {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        .product-detail-dialog .gallery-grid::-webkit-scrollbar {
            width: 6px;
        }

        .product-detail-dialog .gallery-grid::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .product-detail-dialog .gallery-grid::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .product-detail-dialog .gallery-grid::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .product-detail-dialog .image-item {
            transition: all 0.3s ease;
        }

        .product-detail-dialog .image-item:hover {
            transform: scale(1.05);
        }

        /* 系统设置样式 */
        .settings-content {
            padding: 20px;
        }

        .settings-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 30px;
        }

        .settings-tabs .tab-btn {
            padding: 12px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-tabs .tab-btn:hover {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.05);
        }

        .settings-tabs .tab-btn.active {
            color: #ff6b6b;
            border-bottom-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .settings-panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .settings-panel h4 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #ff6b6b;
        }

        .form-control textarea {
            height: 80px;
            resize: vertical;
        }

        .color-picker-group {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .settings-actions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #ff5252 0%, #ff6b6b 100%);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* 统一侧边栏样式 */
        .sidebar-container {
            background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
            border-right: 1px solid #e9ecef;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 4px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #495057;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar-item:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0;
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 100%);
            transition: width 0.3s ease;
            z-index: 0;
        }

        .sidebar-item i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .sidebar-item span {
            position: relative;
            z-index: 1;
        }

        .sidebar-item:hover {
            background: rgba(76, 175, 80, 0.1);
            color: #2e7d32;
            transform: translateX(4px);
        }

        .sidebar-item:hover:before {
            width: 4px;
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.15) 100%);
            color: #2e7d32;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        .sidebar-item.active:before {
            width: 4px;
        }

        .sidebar-item.active i {
            color: #4CAF50;
        }

        /* 管理员信息卡片样式 */
        .sidebar-container .bg-primary\/10 {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(129, 199, 132, 0.1) 100%) !important;
            border: 1px solid rgba(76, 175, 80, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar-container .bg-primary\/10:hover {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.15) 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
        }

        /* 响应式侧边栏 */
        @media (max-width: 768px) {
            .sidebar-item {
                padding: 10px 12px;
                margin: 2px 4px;
            }

            .sidebar-item i {
                margin-right: 8px;
                font-size: 14px;
            }

            .sidebar-item span {
                font-size: 14px;
            }
        }
    </style>
</head>
<body class="min-h-screen font-sans" style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 25%, #A5D6A7 50%, #81C784 75%, #66BB6A 100%);">
<div id="app" class="flex flex-col min-h-screen">
    <!-- 顶部导航栏 -->
    <header style="background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); z-index: 10;">
        <div class="container" style="display: flex; align-items: center; justify-content: space-between; height: 64px;">
            <!-- 品牌标识 -->
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <h1 style="font-size: 1.25rem; font-weight: bold; color: #8B7355; margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    息壤集后台管理系统
                </h1>
            </div>

            <!-- 全局搜索框 -->
            <div style="flex: 1; max-width: 400px; margin: 0 2rem; position: relative;">
                <el-input
                    v-model="globalSearchKeyword"
                    placeholder="搜索用户、产品、分类或功能..."
                    prefix-icon="el-icon-search"
                    @input="performGlobalSearch"
                    @focus="globalSearchVisible = globalSearchResults.length > 0"
                    @blur="setTimeout(() => globalSearchVisible = false, 200)"
                    clearable
                    @clear="clearGlobalSearch">
                </el-input>

                <!-- 搜索结果下拉框 -->
                <div v-if="globalSearchVisible && globalSearchResults.length > 0"
                     style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); z-index: 1000; max-height: 300px; overflow-y: auto;">
                    <div v-for="(result, index) in globalSearchResults" :key="index"
                         @click="executeSearchAction(result)"
                         style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; cursor: pointer; display: flex; align-items: center; gap: 12px;"
                         @mouseover="$event.target.style.backgroundColor = '#f5f5f5'"
                         @mouseout="$event.target.style.backgroundColor = 'white'">
                        <div style="width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 14px;"
                             :style="{ backgroundColor: result.type === 'user' ? '#e3f2fd' : result.type === 'product' ? '#f3e5f5' : result.type === 'category' ? '#e8f5e8' : '#fff3e0' }">
                            <span v-if="result.type === 'user'">👤</span>
                            <span v-else-if="result.type === 'product'">📦</span>
                            <span v-else-if="result.type === 'category'">📂</span>
                            <span v-else>⚙️</span>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 500; color: #333;">{{ result.title }}</div>
                            <div style="font-size: 12px; color: #666;">{{ result.subtitle }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户菜单 -->
            <div style="display: flex; align-items: center; gap: 1rem;">
                <button class="nav-button" style="position: relative;">
                    <span style="font-size: 1.25rem;">🔔</span>
                    <span style="position: absolute; top: 0.25rem; right: 0.25rem; width: 8px; height: 8px; background: #ef4444; border-radius: 50%;"></span>
                </button>
                <div class="user-menu" @click.stop="toggleDropdown">
                    <div style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 0.5rem; border-radius: 0.5rem; transition: background-color 0.3s;" @mouseover="handleUserMenuHover" @mouseout="handleUserMenuLeave">
                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #4CAF50, #81C784); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);">🌱</div>
                        <span style="font-weight: 500; display: none;" class="md:inline">管理员</span>
                        <span style="color: #6b7280;">▼</span>
                    </div>
                    <div class="dropdown-content" :class="{ show: dropdownVisible }" @mouseleave="closeDropdown">
                        <a href="#" class="dropdown-item">
                            <span style="margin-right: 0.5rem;">👤</span>个人信息
                        </a>
                        <a href="#" class="dropdown-item">
                            <span style="margin-right: 0.5rem;">⚙️</span>账号设置
                        </a>
                        <div style="border-top: 1px solid #e5e5e5; margin: 0.25rem 0;"></div>
                        <a href="land.html" class="dropdown-item" style="color: #ef4444;">
                            <span style="margin-right: 0.5rem;">🚪</span>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- 主要内容区 -->
    <div class="flex flex-1 overflow-hidden">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-white shadow-md flex-shrink-0 hidden md:block transition-all duration-300" id="sidebar">
            <div class="sidebar-container h-full">
                <div class="sidebar-content p-4">
                    <div class="mb-6">
                        <div class="flex items-center space-x-3 p-3 bg-primary/10 rounded-lg">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiByeD0iMjUiIGZpbGw9IiNmZjY5YjQiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iMTUiIHk9IjE1Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOS4zMyAxMy45OSA3LjAxIDE1LjYyIDYgMThDMTAuMDEgMjAgMTMuOTkgMjAgMTggMThDMTYuOTkgMTUuNjIgMTQuNjcgMTMuOTkgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="管理员头像" class="w-10 h-10 rounded-full object-cover border-2 border-primary">
                            <div>
                                <h3 class="font-medium text-primary">管理员</h3>
                                <p class="text-xs text-neutral-500">系统管理员</p>
                            </div>
                        </div>
                    </div>
                    <nav>
                        <ul class="space-y-1">
                            <li class="sidebar-item" :class="{ active: activeTab === 'dashboard' }" @click="changeTab('dashboard')">
                                <i class="fa fa-tachometer"></i>
                                <span>仪表盘</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'userManagement' }" @click="changeTab('userManagement')">
                                <i class="fa fa-users"></i>
                                <span>用户管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'productManagement' }" @click="changeTab('productManagement')">
                                <i class="fa fa-shopping-bag"></i>
                                <span>产品管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'categoryManagement' }" @click="changeTab('categoryManagement')">
                                <i class="fa fa-tags"></i>
                                <span>分类管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'carouselManagement' }" @click="changeTab('carouselManagement')">
                                <i class="fa fa-images"></i>
                                <span>轮播图管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'orderManagement' }" @click="changeTab('orderManagement')">
                                <i class="fa fa-shopping-cart"></i>
                                <span>订单管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'favoriteManagement' }" @click="changeTab('favoriteManagement')">
                                <i class="fa fa-heart"></i>
                                <span>收藏管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'commentManagement' }" @click="changeTab('commentManagement')">
                                <i class="fa fa-comment"></i>
                                <span>评论管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'shareManagement' }" @click="changeTab('shareManagement')">
                                <i class="fa fa-share"></i>
                                <span>分享管理</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'dataAnalysis' }" @click="changeTab('dataAnalysis')">
                                <i class="fa fa-bar-chart"></i>
                                <span>数据分析</span>
                            </li>
                            <li class="sidebar-item" :class="{ active: activeTab === 'systemSettings' }" @click="changeTab('systemSettings')">
                                <i class="fa fa-cog"></i>
                                <span>系统设置</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </aside>
        <!-- 主内容 -->
        <main class="flex-1 overflow-auto p-4 md:p-6 bg-neutral-100">

            <!-- 仪表盘页面 -->
            <div v-if="activeTab === 'dashboard'" class="fade-in">
                <!-- 欢迎信息 -->
                <div class="mb-6">
                    <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-neutral-800">欢迎回来，管理员</h2>
                    <p class="text-neutral-600">今天是 <span id="current-date"></span></p>
                </div>
                <!-- 数据概览卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6" style="animation-delay: 0.1s">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总用户数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1" id="total-users">0</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 12% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">🌱</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #81C784; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(129, 199, 132, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">产品总数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1" id="total-products">0</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 8% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(129, 199, 132, 0.1), rgba(165, 214, 167, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(129, 199, 132, 0.2);">
                                <span style="color: #81C784; font-size: 1.25rem;">📦</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #A5D6A7; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(165, 214, 167, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">今日订单</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1" id="today-orders">0</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 5% 较昨日
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(165, 214, 167, 0.1), rgba(200, 230, 201, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(165, 214, 167, 0.2);">
                                <span style="color: #A5D6A7; font-size: 1.25rem;">📋</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #66BB6A; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(102, 187, 106, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">本月销售额</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1" id="monthly-sales">¥0</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 15% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(102, 187, 106, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(102, 187, 106, 0.2);">
                                <span style="color: #66BB6A; font-size: 1.25rem;">💰</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 快速操作 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" @click="changeTab('userManagement')">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-lg text-neutral-800">用户管理</h3>
                                <p class="text-neutral-600 text-sm mt-1">管理系统用户</p>
                            </div>
                            <i class="fa fa-users text-primary text-2xl"></i>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" @click="changeTab('productManagement')">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-lg text-neutral-800">产品管理</h3>
                                <p class="text-neutral-600 text-sm mt-1">管理商品信息</p>
                            </div>
                            <i class="fa fa-shopping-bag text-secondary text-2xl"></i>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" @click="changeTab('categoryManagement')">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-lg text-neutral-800">分类管理</h3>
                                <p class="text-neutral-600 text-sm mt-1">管理商品分类</p>
                            </div>
                            <i class="fa fa-tags text-accent text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 用户管理页面 -->
            <div v-if="activeTab === 'userManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">用户管理</h2>
                    <p class="text-neutral-600">管理系统用户信息</p>
                </div>

                <!-- 用户统计卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #2196F3; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(33, 150, 243, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总用户数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ userStats.total || 0 }}</h3>
                                <p class="text-blue-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 12% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(100, 181, 246, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(33, 150, 243, 0.2);">
                                <span style="color: #2196F3; font-size: 1.25rem;">👥</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">活跃用户</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ userStats.active || 0 }}</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 8% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">✅</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #FF9800; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 152, 0, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">今日新增</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ userStats.todayNew || 0 }}</h3>
                                <p class="text-orange-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 5% 较昨日
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 183, 77, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(255, 152, 0, 0.2);">
                                <span style="color: #FF9800; font-size: 1.25rem;">🆕</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #9C27B0; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(156, 39, 176, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">VIP用户</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ userStats.vip || 0 }}</h3>
                                <p class="text-purple-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 15% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(186, 104, 200, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(156, 39, 176, 0.2);">
                                <span style="color: #9C27B0; font-size: 1.25rem;">👑</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-md">
                    <!-- 搜索和筛选区域 -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索用户</label>
                                <el-input
                                    v-model="userSearchForm.keyword"
                                    placeholder="输入用户名、手机号或邮箱"
                                    prefix-icon="el-icon-search"
                                    @input="onUserSearchInput"
                                    @clear="searchUsers"
                                    clearable>
                                </el-input>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
                                <el-select v-model="userSearchForm.status" placeholder="选择状态" @change="searchUsers" clearable>
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="正常" value="active"></el-option>
                                    <el-option label="禁用" value="disabled"></el-option>
                                    <el-option label="待验证" value="pending"></el-option>
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">注册时间</label>
                                <el-date-picker
                                    v-model="userSearchForm.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    @change="searchUsers"
                                    size="small">
                                </el-date-picker>
                            </div>
                            <div class="flex items-end">
                                <el-button type="primary" @click="searchUsers" icon="el-icon-search">搜索</el-button>
                                <el-button @click="resetUserSearch" icon="el-icon-refresh">重置</el-button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">用户列表</h3>
                        <div class="flex gap-2">
                            <el-button type="success" @click="exportUsers" icon="el-icon-download">导出用户</el-button>
                            <el-button type="primary" @click="addUserDialogVisible = true" icon="el-icon-plus">新增用户</el-button>
                        </div>
                    </div>
                    <el-table :data="userList" stripe style="width: 100%">
                        <el-table-column prop="userId" label="用户ID" width="100"></el-table-column>
                        <el-table-column prop="username" label="用户名"></el-table-column>
                        <el-table-column prop="phone" label="手机号"></el-table-column>
                        <el-table-column label="注册时间" width="180">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.registerTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200">
                            <template #default="scope">
                                <el-button size="mini" type="text" @click="viewUserDetail(scope.row)">详情</el-button>
                                <el-button size="mini" type="text" @click="editUser(scope.row)">编辑</el-button>
                                <el-button size="mini" type="text" @click="deleteUser(scope.row.userId)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页组件 -->
                    <div class="mt-4">
                        <el-pagination
                                @size-change="handleUserSizeChange"
                                @current-change="handleUserCurrentChange"
                                :current-page="userPagination.currentPage"
                                :page-sizes="[5, 10, 20, 50]"
                                :page-size="userPagination.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="userPagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>
            <!-- 产品管理页面 -->
            <div v-if="activeTab === 'productManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">产品管理</h2>
                    <p class="text-neutral-600">管理商品信息和库存</p>
                </div>

                <!-- 搜索和筛选区域 -->
                <div class="bg-white rounded-xl p-4 shadow-md mb-4">
                    <div class="flex flex-wrap gap-4 items-center">
                        <el-input
                            v-model="productSearchForm.name"
                            placeholder="搜索产品名称或描述"
                            style="width: 250px;"
                            @input="onProductSearchInput"
                            @keyup.enter="searchProducts"
                            @clear="searchProducts"
                            clearable>
                            <template #prefix>
                                <i class="el-icon-search"></i>
                            </template>
                        </el-input>

                        <el-select v-model="productSearchForm.category"
                                   placeholder="选择分类"
                                   style="width: 150px;"
                                   clearable
                                   @change="onProductSearchInput">
                            <el-option label="全部分类" value=""></el-option>
                            <el-option
                                v-for="category in categoryList"
                                :key="category.id"
                                :label="category.name"
                                :value="category.id">
                            </el-option>
                        </el-select>

                        <el-button @click="searchProducts" type="primary" icon="el-icon-search">搜索</el-button>
                        <el-button @click="resetProductSearch" icon="el-icon-refresh">重置</el-button>
                        <el-button type="success" @click="addProductDialogVisible = true" icon="el-icon-plus">新增产品</el-button>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">产品列表</h3>
                        <div class="flex gap-2">
                            <el-button size="small" @click="exportProducts" icon="el-icon-download">导出</el-button>
                            <el-button size="small" @click="batchDeleteProducts" type="danger" :disabled="selectedProducts.length === 0">
                                批量删除 ({{ selectedProducts.length }})
                            </el-button>
                        </div>
                    </div>

                    <el-table
                        :data="productList"
                        stripe
                        style="width: 100%"
                        @selection-change="handleProductSelectionChange"
                        v-loading="productLoading">
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="id" label="ID" width="80" sortable></el-table-column>
                        <el-table-column label="图片" width="100">
                            <template #default="scope">
                                <div class="relative">
                                    <img :src="scope.row.primary_image_url || scope.row.imageUrl || '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg'"
                                         alt="产品图片"
                                         class="w-12 h-12 rounded object-cover cursor-pointer"
                                         @error="handleImageError"
                                         @click="previewProductImages(scope.row)"
                                         style="max-width: 80px; max-height: 80px;">
                                    <el-badge v-if="scope.row.imageCount > 1" :value="scope.row.imageCount" class="absolute top-0 right-0"></el-badge>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="产品名称" min-width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="description" label="描述" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="price" label="价格" width="100" sortable>
                            <template #default="scope">
                                <span class="font-semibold text-green-600">¥{{ scope.row.price }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="stock" label="库存" width="100" sortable>
                            <template #default="scope">
                                <el-tag :type="scope.row.stock > 50 ? 'success' : scope.row.stock > 20 ? 'warning' : 'danger'" size="mini">
                                    {{ scope.row.stock }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="新品" width="80">
                            <template #default="scope">
                                <el-switch
                                    v-model="scope.row.isNew"
                                    @change="toggleProductNewStatus(scope.row)"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="80">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
                                    {{ scope.row.status === 1 ? '上架' : '下架' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200" fixed="right">
                            <template #default="scope">
                                <el-button size="mini" type="text" @click="viewProductDetail(scope.row)" icon="el-icon-view">详情</el-button>
                                <el-button size="mini" type="text" @click="editProduct(scope.row)" icon="el-icon-edit">编辑</el-button>
                                <el-button size="mini" type="text" @click="manageProductImages(scope.row)" icon="el-icon-picture">图片</el-button>
                                <el-dropdown @command="handleProductAction" trigger="click">
                                    <el-button size="mini" type="text">
                                        更多<i class="el-icon-arrow-down el-icon--right"></i>
                                    </el-button>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item :command="{action: 'toggleStatus', row: scope.row}">
                                            {{ scope.row.status === 1 ? '下架' : '上架' }}
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{action: 'copy', row: scope.row}">复制产品</el-dropdown-item>
                                        <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            共 {{ productPagination.total }} 条记录，当前显示第 {{ (productPagination.currentPage - 1) * productPagination.pageSize + 1 }} -
                            {{ Math.min(productPagination.currentPage * productPagination.pageSize, productPagination.total) }} 条
                        </div>
                        <el-pagination
                                @size-change="handleProductSizeChange"
                                @current-change="handleProductCurrentChange"
                                :current-page="productPagination.currentPage"
                                :page-sizes="[5, 10, 20, 50]"
                                :page-size="productPagination.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="productPagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 轮播图管理页面 -->
            <div v-if="activeTab === 'carouselManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">轮播图管理</h2>
                    <p class="text-neutral-600">管理首页轮播图片和内容</p>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">轮播图列表</h3>
                        <el-button type="primary" @click="addCarouselDialogVisible = true">新增轮播图</el-button>
                    </div>
                    <el-table :data="carouselList" stripe style="width: 100%">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column label="预览图" width="120">
                            <template #default="scope">
                                <img :src="scope.row.image_url || scope.row.imageUrl || '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg'"
                                     alt="轮播图"
                                     class="w-16 h-10 rounded object-cover"
                                     @error="handleImageError"
                                     style="max-width: 100px; max-height: 60px;"
                                     @load="handleImageLoad">
                            </template>
                        </el-table-column>
                        <el-table-column prop="title" label="标题" width="200"></el-table-column>
                        <el-table-column prop="description" label="描述" width="250" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="category_name" label="关联分类" width="120"></el-table-column>
                        <el-table-column prop="sort_order" label="排序" width="80"></el-table-column>
                        <el-table-column label="状态" width="80">
                            <template #default="scope">
                                <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                                    {{ scope.row.is_active ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200">
                            <template #default="scope">
                                <el-button size="mini" @click="editCarousel(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="deleteCarousel(scope.row.id)">删除</el-button>
                                <el-button size="mini" :type="scope.row.is_active ? 'warning' : 'success'"
                                          @click="toggleCarouselStatus(scope.row)">
                                    {{ scope.row.is_active ? '禁用' : '启用' }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!-- 分类管理页面 -->
            <div v-if="activeTab === 'categoryManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">分类管理</h2>
                    <p class="text-neutral-600">管理商品分类信息</p>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">分类列表</h3>
                        <el-button type="primary" @click="addCategoryDialogVisible = true">新增分类</el-button>
                    </div>
                    <el-table :data="categoryList" stripe style="width: 100%">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="name" label="分类名称"></el-table-column>
                        <el-table-column label="产品数量" width="120">
                            <template #default="scope">
                                <span class="text-sm bg-primary/10 text-primary px-2 py-1 rounded-full">
                                    {{ scope.row.product_count || 0 }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" width="180">
                            <template #default="scope">
                                {{ formatDate(scope.row.createTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150">
                            <template #default="scope">
                                <el-button size="mini" type="text" @click="editCategory(scope.row)">编辑</el-button>
                                <el-button size="mini" type="text" @click="deleteCategory(scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页组件 -->
                    <div class="mt-4">
                        <el-pagination
                                @size-change="handleCategorySizeChange"
                                @current-change="handleCategoryCurrentChange"
                                :current-page="categoryPagination.currentPage"
                                :page-sizes="[5, 10, 20, 50]"
                                :page-size="categoryPagination.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="categoryPagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>
            <!-- 数据分析页面 -->
            <div v-if="activeTab === 'dataAnalysis'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">数据分析</h2>
                    <p class="text-neutral-600">查看销售数据和业务分析</p>
                </div>
                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-xl p-6 shadow-md">
                        <h3 class="font-semibold text-lg mb-4">销售趋势</h3>
                        <div class="h-80">
                            <!-- 销售趋势图表将在这里渲染 -->
                            <canvas id="salesChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-6 shadow-md">
                        <h3 class="font-semibold text-lg mb-4">产品分类分布</h3>
                        <div class="h-80">
                            <!-- 产品分类图表将在这里渲染 -->
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
                <!-- 数据统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <div class="bg-white rounded-xl p-6 shadow-md">
                        <h4 class="font-semibold text-lg mb-3">销售统计</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-neutral-600">今日销售额</span>
                                <span class="font-semibold">¥{{ analysisData.todaySales || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">本月销售额</span>
                                <span class="font-semibold">¥{{ analysisData.monthlySales || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">年度销售额</span>
                                <span class="font-semibold">¥{{ analysisData.yearlySales || '0' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-6 shadow-md">
                        <h4 class="font-semibold text-lg mb-3">订单统计</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-neutral-600">今日订单</span>
                                <span class="font-semibold">{{ analysisData.todayOrders || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">本月订单</span>
                                <span class="font-semibold">{{ analysisData.monthlyOrders || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">总订单数</span>
                                <span class="font-semibold">{{ orderStats.totalOrders || '0' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-6 shadow-md">
                        <h4 class="font-semibold text-lg mb-3">用户统计</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-neutral-600">新增用户</span>
                                <span class="font-semibold">{{ analysisData.newUsers || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">活跃用户</span>
                                <span class="font-semibold">{{ analysisData.activeUsers || '0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-600">总用户数</span>
                                <span class="font-semibold">{{ userPagination.total || '0' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单管理页面 -->
            <div v-if="activeTab === 'orderManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">订单管理</h2>
                    <p class="text-neutral-600">管理客户订单信息</p>
                </div>

                <!-- 订单统计卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #2196F3; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(33, 150, 243, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总订单数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ orderStats.totalOrders || 0 }}</h3>
                                <p class="text-blue-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 12% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(100, 181, 246, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(33, 150, 243, 0.2);">
                                <span style="color: #2196F3; font-size: 1.25rem;">🛒</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #FF9800; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 152, 0, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">待处理</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ orderStats.pendingOrders || 0 }}</h3>
                                <p class="text-orange-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">⏳</span> 需要处理
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 183, 77, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(255, 152, 0, 0.2);">
                                <span style="color: #FF9800; font-size: 1.25rem;">⏰</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">已完成</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ orderStats.completedOrders || 0 }}</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">✅</span> 完成率 85%
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">✅</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #9C27B0; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(156, 39, 176, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总销售额</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">¥{{ orderStats.totalAmount || 0 }}</h3>
                                <p class="text-purple-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 15% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(186, 104, 200, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(156, 39, 176, 0.2);">
                                <span style="color: #9C27B0; font-size: 1.25rem;">💰</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">订单列表</h3>
                        <div class="flex space-x-2">
                            <el-select v-model="orderStatusFilter" placeholder="筛选状态" size="small" style="width: 120px;">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="待付款" value="1"></el-option>
                                <el-option label="待发货" value="2"></el-option>
                                <el-option label="待收货" value="3"></el-option>
                                <el-option label="已完成" value="4"></el-option>
                                <el-option label="已取消" value="5"></el-option>
                            </el-select>
                            <el-button @click="loadOrders" size="small" icon="el-icon-refresh">刷新</el-button>
                        </div>
                    </div>

                    <el-table :data="orderList" style="width: 100%" v-loading="orderLoading">
                        <el-table-column prop="orderNo" label="订单号" width="180"></el-table-column>
                        <el-table-column prop="userPhone" label="用户手机" width="120"></el-table-column>
                        <el-table-column prop="totalAmount" label="订单金额" width="100">
                            <template slot-scope="scope">
                                ¥{{ scope.row.totalAmount }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" label="订单状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getOrderStatusType(scope.row.status)">
                                    {{ getOrderStatusText(scope.row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="receiverName" label="收货人" width="100"></el-table-column>
                        <el-table-column prop="receiverPhone" label="收货电话" width="120"></el-table-column>
                        <el-table-column prop="createdTime" label="下单时间" width="160">
                            <template slot-scope="scope">
                                {{ formatTime(scope.row.createdTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="250">
                            <template slot-scope="scope">
                                <el-button @click="viewOrderDetail(scope.row)" size="mini" type="primary" icon="el-icon-view">详情</el-button>
                                <el-button v-if="scope.row.status === 2" @click="shipOrder(scope.row)" size="mini" type="success" icon="el-icon-truck">发货</el-button>
                                <el-button v-if="scope.row.status === 1" @click="cancelOrderAdmin(scope.row)" size="mini" type="warning" icon="el-icon-close">取消</el-button>
                                <el-button @click="deleteOrder(scope.row)" size="mini" type="danger" icon="el-icon-delete">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="flex justify-center mt-4">
                        <el-pagination
                            @size-change="handleOrderSizeChange"
                            @current-change="handleOrderCurrentChange"
                            :current-page="orderCurrentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="orderPageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="orderTotal">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 收藏管理页面 -->
            <div v-if="activeTab === 'favoriteManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">收藏管理</h2>
                    <p class="text-neutral-600">管理用户收藏的商品信息</p>
                </div>

                <!-- 收藏统计卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #E91E63; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(233, 30, 99, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总收藏数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ favoriteStats.total || 0 }}</h3>
                                <p class="text-pink-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 12% 较上月
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(240, 98, 146, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(233, 30, 99, 0.2);">
                                <span style="color: #E91E63; font-size: 1.25rem;">❤️</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #9C27B0; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(156, 39, 176, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">今日新增</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ favoriteStats.todayNew || 0 }}</h3>
                                <p class="text-purple-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">↑</span> 8% 较昨日
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(186, 104, 200, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(156, 39, 176, 0.2);">
                                <span style="color: #9C27B0; font-size: 1.25rem;">📈</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #FF5722; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 87, 34, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">热门商品</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ favoriteStats.popularProduct || 0 }}</h3>
                                <p class="text-red-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">🔥</span> 最高收藏
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(255, 87, 34, 0.1), rgba(255, 138, 101, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(255, 87, 34, 0.2);">
                                <span style="color: #FF5722; font-size: 1.25rem;">🔥</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">活跃用户</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ favoriteStats.activeUsers || 0 }}</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">👥</span> 有收藏行为
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">👥</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-md">
                    <!-- 搜索和筛选区域 -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                                <el-input
                                    v-model="favoriteSearchForm.keyword"
                                    placeholder="输入用户手机号或商品名称"
                                    prefix-icon="el-icon-search"
                                    @input="onFavoriteSearchInput"
                                    @clear="searchFavorites"
                                    clearable>
                                </el-input>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
                                <el-select v-model="favoriteSearchForm.category" placeholder="选择分类" @change="searchFavorites" clearable>
                                    <el-option label="全部分类" value=""></el-option>
                                    <el-option
                                        v-for="category in categoryList"
                                        :key="category.id"
                                        :label="category.name"
                                        :value="category.id">
                                    </el-option>
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">收藏时间</label>
                                <el-date-picker
                                    v-model="favoriteSearchForm.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    @change="searchFavorites"
                                    size="small">
                                </el-date-picker>
                            </div>
                            <div class="flex items-end">
                                <el-button type="primary" @click="searchFavorites" icon="el-icon-search">搜索</el-button>
                                <el-button @click="resetFavoriteSearch" icon="el-icon-refresh">重置</el-button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">收藏列表</h3>
                        <div class="flex gap-2">
                            <el-button type="success" @click="exportFavorites" icon="el-icon-download">导出数据</el-button>
                            <el-button type="info" @click="viewPopularProducts" icon="el-icon-star-on">热门商品</el-button>
                        </div>
                    </div>

                    <el-table :data="favoriteList" stripe style="width: 100%" v-loading="favoriteLoading">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column label="用户信息" width="180">
                            <template #default="scope">
                                <div class="flex items-center gap-2">
                                    <img :src="getAvatarUrl(scope.row.userAvatar)"
                                         alt="用户头像"
                                         class="w-8 h-8 rounded-full object-cover"
                                         @error="$event.target.src='/images/avatars/default-avatar.svg'">
                                    <div>
                                        <div class="font-medium text-sm">{{ scope.row.userName || '匿名用户' }}</div>
                                        <div class="text-xs text-gray-500">电话: {{ scope.row.userPhone }}</div>
                                        <div class="text-xs text-blue-500">账户: {{ scope.row.userId || scope.row.userPhone }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="商品信息" min-width="200">
                            <template #default="scope">
                                <div class="flex items-center gap-3">
                                    <img :src="scope.row.productImageUrl || '/images/default-product.svg'"
                                         alt="商品图片"
                                         class="w-12 h-12 rounded object-cover"
                                         @error="handleImageError">
                                    <div>
                                        <div class="font-medium">{{ scope.row.productName }}</div>
                                        <div class="text-sm text-red-600 font-semibold">¥{{ scope.row.productPrice }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="收藏时间" width="180">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.createdTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150">
                            <template #default="scope">
                                <el-button size="mini" type="text" @click="viewFavoriteDetail(scope.row)">详情</el-button>
                                <el-button size="mini" type="text" @click="deleteFavorite(scope.row.id)" style="color: #f56c6c;">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="mt-4">
                        <el-pagination
                            @size-change="handleFavoriteSizeChange"
                            @current-change="handleFavoriteCurrentChange"
                            :current-page="favoritePagination.currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="favoritePagination.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="favoritePagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 评论管理页面 -->
            <div v-if="activeTab === 'commentManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">评论管理</h2>
                    <p class="text-neutral-600">管理用户评论和审核</p>
                </div>

                <!-- 评论统计卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #2196F3; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(33, 150, 243, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总评论数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ commentStats.total || 0 }}</h3>
                                <p class="text-blue-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">💬</span> 用户反馈
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(100, 181, 246, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(33, 150, 243, 0.2);">
                                <span style="color: #2196F3; font-size: 1.25rem;">💬</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #FF9800; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 152, 0, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">待审核</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ commentStats.pending || 0 }}</h3>
                                <p class="text-orange-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">⏳</span> 需要审核
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 183, 77, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(255, 152, 0, 0.2);">
                                <span style="color: #FF9800; font-size: 1.25rem;">⏳</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">已通过</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ commentStats.approved || 0 }}</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">✅</span> 审核通过
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">✅</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #F44336; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(244, 67, 54, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">已拒绝</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ commentStats.rejected || 0 }}</h3>
                                <p class="text-red-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">❌</span> 审核拒绝
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(239, 154, 154, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(244, 67, 54, 0.2);">
                                <span style="color: #F44336; font-size: 1.25rem;">❌</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-md">
                    <!-- 搜索和筛选区域 -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                                <el-input
                                    v-model="commentSearchForm.keyword"
                                    placeholder="输入用户手机号或评论内容"
                                    prefix-icon="el-icon-search"
                                    @input="onCommentSearchInput"
                                    @clear="searchComments"
                                    clearable>
                                </el-input>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">审核状态</label>
                                <el-select v-model="commentSearchForm.status" placeholder="选择状态" @change="searchComments" clearable>
                                    <el-option label="全部状态" value=""></el-option>
                                    <el-option label="待审核" value="0"></el-option>
                                    <el-option label="已通过" value="1"></el-option>
                                    <el-option label="已拒绝" value="2"></el-option>
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">评分</label>
                                <el-select v-model="commentSearchForm.rating" placeholder="选择评分" @change="searchComments" clearable>
                                    <el-option label="全部评分" value=""></el-option>
                                    <el-option label="5星" value="5"></el-option>
                                    <el-option label="4星" value="4"></el-option>
                                    <el-option label="3星" value="3"></el-option>
                                    <el-option label="2星" value="2"></el-option>
                                    <el-option label="1星" value="1"></el-option>
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">评论时间</label>
                                <el-date-picker
                                    v-model="commentSearchForm.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    @change="searchComments"
                                    size="small">
                                </el-date-picker>
                            </div>
                            <div class="flex items-end">
                                <el-button type="primary" @click="searchComments" icon="el-icon-search">搜索</el-button>
                                <el-button @click="resetCommentSearch" icon="el-icon-refresh">重置</el-button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">评论列表</h3>
                        <div class="flex gap-2">
                            <el-button type="success" @click="batchApproveComments" icon="el-icon-check">批量通过</el-button>
                            <el-button type="warning" @click="batchRejectComments" icon="el-icon-close">批量拒绝</el-button>
                            <el-button type="info" @click="exportComments" icon="el-icon-download">导出数据</el-button>
                        </div>
                    </div>

                    <el-table :data="commentList" stripe style="width: 100%" v-loading="commentLoading" @selection-change="handleCommentSelectionChange">
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column label="用户信息" width="180">
                            <template #default="scope">
                                <div class="flex items-center gap-2">
                                    <img :src="getAvatarUrl(scope.row.userAvatar)"
                                         alt="用户头像"
                                         class="w-8 h-8 rounded-full object-cover"
                                         @error="$event.target.src='/images/avatars/default-avatar.svg'">
                                    <div>
                                        <div class="font-medium text-sm">{{ scope.row.userName || '匿名用户' }}</div>
                                        <div class="text-xs text-gray-500">电话: {{ scope.row.userPhone }}</div>
                                        <div class="text-xs text-blue-500">账户: {{ scope.row.userId || scope.row.userPhone }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="商品信息" width="200">
                            <template #default="scope">
                                <div class="flex items-center gap-3">
                                    <img :src="scope.row.productImageUrl || '/images/default-product.svg'"
                                         alt="商品图片"
                                         class="w-12 h-12 rounded object-cover">
                                    <div>
                                        <div class="font-medium text-sm">{{ scope.row.productName }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="评论内容" min-width="250">
                            <template #default="scope">
                                <div>
                                    <div class="flex items-center gap-2 mb-2">
                                        <div class="text-yellow-500">
                                            <span v-for="i in scope.row.rating" :key="i">★</span>
                                            <span v-for="i in (5 - scope.row.rating)" :key="i + scope.row.rating">☆</span>
                                        </div>
                                        <span class="text-sm text-gray-500">({{ scope.row.rating }}分)</span>
                                    </div>
                                    <p class="text-sm text-gray-700 line-clamp-2">{{ scope.row.content }}</p>
                                    <div v-if="scope.row.images" class="flex gap-1 mt-2">
                                        <img v-for="(image, index) in scope.row.images.split(',')"
                                             :key="index"
                                             :src="image"
                                             alt="评论图片"
                                             class="w-8 h-8 rounded object-cover cursor-pointer"
                                             @click="previewImage(image)">
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100">
                            <template #default="scope">
                                <el-tag :type="getCommentStatusType(scope.row.status)" size="small">
                                    {{ getCommentStatusText(scope.row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="评论时间" width="160">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.createdTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200">
                            <template #default="scope">
                                <el-button v-if="scope.row.status === 0" size="mini" type="success" @click="approveComment(scope.row.id)">通过</el-button>
                                <el-button v-if="scope.row.status === 0" size="mini" type="warning" @click="rejectComment(scope.row.id)">拒绝</el-button>
                                <el-button size="mini" type="primary" @click="viewCommentDetail(scope.row)">详情</el-button>
                                <el-button size="mini" type="danger" @click="deleteComment(scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="mt-4">
                        <el-pagination
                            @size-change="handleCommentSizeChange"
                            @current-change="handleCommentCurrentChange"
                            :current-page="commentPagination.currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="commentPagination.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="commentPagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 分享管理页面 -->
            <div v-if="activeTab === 'shareManagement'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">分享管理</h2>
                    <p class="text-neutral-600">管理用户分享数据和统计</p>
                </div>

                <!-- 分享统计卡片 -->
                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-primary" style="border-left: 4px solid #673AB7; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(103, 58, 183, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">总分享数</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ shareStats.total || 0 }}</h3>
                                <p class="text-indigo-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">📤</span> 用户分享
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(103, 58, 183, 0.1), rgba(149, 117, 205, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(103, 58, 183, 0.2);">
                                <span style="color: #673AB7; font-size: 1.25rem;">📤</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-secondary" style="border-left: 4px solid #4CAF50; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(76, 175, 80, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">微信分享</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ shareStats.wechat || 0 }}</h3>
                                <p class="text-green-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">💬</span> 最受欢迎
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(76, 175, 80, 0.2);">
                                <span style="color: #4CAF50; font-size: 1.25rem;">💬</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-accent" style="border-left: 4px solid #F44336; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(244, 67, 54, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">微博分享</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ shareStats.weibo || 0 }}</h3>
                                <p class="text-red-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">📱</span> 社交媒体
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(239, 154, 154, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(244, 67, 54, 0.2);">
                                <span style="color: #F44336; font-size: 1.25rem;">📱</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-5 shadow-md hover:shadow-lg transition-all duration-300 dashboard-card-green" style="border-left: 4px solid #2196F3; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(33, 150, 243, 0.1);">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-neutral-500 text-sm">QQ分享</p>
                                <h3 class="text-2xl font-bold text-neutral-800 mt-1">{{ shareStats.qq || 0 }}</h3>
                                <p class="text-blue-500 text-xs mt-2 flex items-center">
                                    <span style="margin-right: 0.25rem;">🐧</span> 即时通讯
                                </p>
                            </div>
                            <div style="background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(100, 181, 246, 0.1)); padding: 0.75rem; border-radius: 0.5rem; border: 1px solid rgba(33, 150, 243, 0.2);">
                                <span style="color: #2196F3; font-size: 1.25rem;">🐧</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-md">
                    <!-- 搜索和筛选区域 -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                                <el-input
                                    v-model="shareSearchForm.keyword"
                                    placeholder="输入用户手机号或商品名称"
                                    prefix-icon="el-icon-search"
                                    @input="onShareSearchInput"
                                    @clear="searchShares"
                                    clearable>
                                </el-input>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">分享类型</label>
                                <el-select v-model="shareSearchForm.shareType" placeholder="选择类型" @change="searchShares" clearable>
                                    <el-option label="全部类型" value=""></el-option>
                                    <el-option label="微信" value="wechat"></el-option>
                                    <el-option label="微博" value="weibo"></el-option>
                                    <el-option label="QQ" value="qq"></el-option>
                                    <el-option label="复制链接" value="link"></el-option>
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">分享时间</label>
                                <el-date-picker
                                    v-model="shareSearchForm.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    @change="searchShares"
                                    size="small">
                                </el-date-picker>
                            </div>
                            <div class="flex items-end">
                                <el-button type="primary" @click="searchShares" icon="el-icon-search">搜索</el-button>
                                <el-button @click="resetShareSearch" icon="el-icon-refresh">重置</el-button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-lg">分享列表</h3>
                        <div class="flex gap-2">
                            <el-button type="success" @click="exportShares" icon="el-icon-download">导出数据</el-button>
                            <el-button type="info" @click="viewShareStats" icon="el-icon-pie-chart">分享统计</el-button>
                        </div>
                    </div>

                    <el-table :data="shareList" stripe style="width: 100%" v-loading="shareLoading">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column label="用户信息" width="180">
                            <template #default="scope">
                                <div class="flex items-center gap-2">
                                    <img :src="getAvatarUrl(scope.row.userAvatar)"
                                         alt="用户头像"
                                         class="w-8 h-8 rounded-full object-cover"
                                         @error="$event.target.src='/images/avatars/default-avatar.svg'">
                                    <div>
                                        <div class="font-medium text-sm">{{ scope.row.userName || '匿名用户' }}</div>
                                        <div class="text-xs text-gray-500">电话: {{ scope.row.userPhone }}</div>
                                        <div class="text-xs text-blue-500">账户: {{ scope.row.userId || scope.row.userPhone }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="商品信息" min-width="200">
                            <template #default="scope">
                                <div class="flex items-center gap-3">
                                    <img :src="scope.row.productImageUrl || '/images/default-product.svg'"
                                         alt="商品图片"
                                         class="w-12 h-12 rounded object-cover">
                                    <div>
                                        <div class="font-medium">{{ scope.row.productName }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="分享类型" width="120">
                            <template #default="scope">
                                <el-tag :type="getShareTypeColor(scope.row.shareType)" size="small">
                                    {{ getShareTypeText(scope.row.shareType) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="分享时间" width="180">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.createdTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <el-button size="mini" type="text" @click="viewShareDetail(scope.row)">详情</el-button>
                                <el-button size="mini" type="text" @click="deleteShare(scope.row.id)" style="color: #f56c6c;">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="mt-4">
                        <el-pagination
                            @size-change="handleShareSizeChange"
                            @current-change="handleShareCurrentChange"
                            :current-page="sharePagination.currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="sharePagination.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="sharePagination.total">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div v-if="activeTab === 'systemSettings'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">系统设置</h2>
                    <p class="text-neutral-600">配置系统参数和选项</p>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="settings-content">
                        <div class="settings-tabs">
                            <button class="tab-btn active" @click="currentSettingsTab = 'general'">
                                <i class="fa fa-cog"></i> 常规设置
                            </button>
                            <button class="tab-btn" @click="currentSettingsTab = 'appearance'">
                                <i class="fa fa-paint-brush"></i> 外观设置
                            </button>
                            <button class="tab-btn" @click="currentSettingsTab = 'notification'">
                                <i class="fa fa-bell"></i> 通知设置
                            </button>
                        </div>

                        <div class="settings-panel" v-if="currentSettingsTab === 'general'">
                            <h4 class="mb-4">网站基本设置</h4>
                            <div class="form-group">
                                <label>网站名称</label>
                                <input type="text" class="form-control" v-model="systemSettings.siteName" placeholder="息壤集">
                            </div>
                            <div class="form-group">
                                <label>网站描述</label>
                                <textarea class="form-control" v-model="systemSettings.siteDescription" placeholder="美妆购物网站"></textarea>
                            </div>
                            <div class="form-group">
                                <label>联系邮箱</label>
                                <input type="email" class="form-control" v-model="systemSettings.contactEmail" placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="settings-panel" v-if="currentSettingsTab === 'appearance'">
                            <h4 class="mb-4">主题外观</h4>
                            <div class="form-group">
                                <label>主题色彩</label>
                                <div class="color-picker-group">
                                    <div class="color-option" style="background: #ff6b6b" @click="systemSettings.themeColor = '#ff6b6b'"></div>
                                    <div class="color-option" style="background: #4ecdc4" @click="systemSettings.themeColor = '#4ecdc4'"></div>
                                    <div class="color-option" style="background: #45b7d1" @click="systemSettings.themeColor = '#45b7d1'"></div>
                                    <div class="color-option" style="background: #96ceb4" @click="systemSettings.themeColor = '#96ceb4'"></div>
                                </div>
                            </div>
                        </div>

                        <div class="settings-panel" v-if="currentSettingsTab === 'notification'">
                            <h4 class="mb-4">通知设置</h4>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" v-model="systemSettings.emailNotification">
                                    <span>邮件通知</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" v-model="systemSettings.orderNotification">
                                    <span>订单通知</span>
                                </label>
                            </div>
                        </div>

                        <div class="settings-actions">
                            <button class="btn btn-primary" @click="saveSystemSettings">保存设置</button>
                            <button class="btn btn-secondary" @click="resetSystemSettings">重置</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div v-if="activeTab === 'systemSettings'" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-neutral-800">系统设置</h2>
                    <p class="text-neutral-600">配置系统参数和功能设置</p>
                </div>

                <!-- 设置分类卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <!-- 基本设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('basic')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-setting text-blue-600 text-xl"></i>
                            </div>
                            <el-tag type="success" size="mini">已配置</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">基本设置</h3>
                        <p class="text-gray-600 text-sm">网站名称、Logo、联系方式等基本信息配置</p>
                    </div>

                    <!-- 邮件设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('email')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-message text-green-600 text-xl"></i>
                            </div>
                            <el-tag type="warning" size="mini">待配置</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">邮件设置</h3>
                        <p class="text-gray-600 text-sm">SMTP服务器配置，用于发送系统邮件通知</p>
                    </div>

                    <!-- 支付设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('payment')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-wallet text-yellow-600 text-xl"></i>
                            </div>
                            <el-tag type="info" size="mini">部分配置</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">支付设置</h3>
                        <p class="text-gray-600 text-sm">支付宝、微信支付等第三方支付接口配置</p>
                    </div>

                    <!-- 短信设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('sms')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-phone text-purple-600 text-xl"></i>
                            </div>
                            <el-tag type="danger" size="mini">未配置</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">短信设置</h3>
                        <p class="text-gray-600 text-sm">短信验证码、通知短信等服务配置</p>
                    </div>

                    <!-- 存储设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('storage')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-folder text-indigo-600 text-xl"></i>
                            </div>
                            <el-tag type="success" size="mini">已配置</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">存储设置</h3>
                        <p class="text-gray-600 text-sm">文件上传、图片存储、CDN等配置</p>
                    </div>

                    <!-- 安全设置 -->
                    <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                         @click="openSettingsDialog('security')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="el-icon-lock text-red-600 text-xl"></i>
                            </div>
                            <el-tag type="warning" size="mini">需检查</el-tag>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">安全设置</h3>
                        <p class="text-gray-600 text-sm">密码策略、登录限制、API安全等配置</p>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="el-icon-info mr-2 text-blue-600"></i>
                            系统信息
                        </h3>
                        <el-button size="small" @click="updateSystemInfo" icon="el-icon-refresh">刷新</el-button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                            <div class="text-2xl font-bold text-blue-600 mb-2">{{ systemInfo.version }}</div>
                            <div class="text-sm text-gray-600">系统版本</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                            <div class="text-2xl font-bold text-green-600 mb-2">{{ systemInfo.uptime }}</div>
                            <div class="text-sm text-gray-600">运行时间</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200">
                            <div class="text-2xl font-bold text-yellow-600 mb-2">{{ systemInfo.storage }}</div>
                            <div class="text-sm text-gray-600">存储使用</div>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                            <div class="text-2xl font-bold text-purple-600 mb-2">{{ systemInfo.memory }}</div>
                            <div class="text-sm text-gray-600">内存使用</div>
                        </div>
                    </div>

                    <!-- 详细系统信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">数据库版本</span>
                                <span class="font-semibold">{{ systemInfo.database }}</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">Java版本</span>
                                <span class="font-semibold">{{ systemInfo.javaVersion }}</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">活跃用户</span>
                                <span class="font-semibold text-blue-600">{{ systemInfo.activeUsers }}</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">最后备份</span>
                                <span class="font-semibold">{{ systemInfo.lastBackup }}</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">今日订单</span>
                                <span class="font-semibold text-green-600">{{ systemInfo.todayOrders }}</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">系统状态</span>
                                <el-tag type="success" size="small">运行正常</el-tag>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统维护 -->
                <div class="bg-white rounded-xl p-6 shadow-md mt-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="el-icon-setting mr-2 text-orange-600"></i>
                        系统维护
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <el-button type="primary" @click="performSystemMaintenance('clearCache')" icon="el-icon-delete">
                            清理缓存
                        </el-button>
                        <el-button type="success" @click="performSystemMaintenance('backup')" icon="el-icon-download">
                            系统备份
                        </el-button>
                        <el-button type="warning" @click="performSystemMaintenance('optimize')" icon="el-icon-magic-stick">
                            数据库优化
                        </el-button>
                        <el-button type="danger" @click="performSystemMaintenance('restart')" icon="el-icon-refresh-right">
                            重启系统
                        </el-button>
                    </div>
                </div>

                <!-- 操作日志 -->
                <div class="bg-white rounded-xl p-6 shadow-md mt-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="el-icon-document mr-2 text-green-600"></i>
                            最近操作日志
                        </h3>
                        <el-button size="small" @click="viewAllLogs">查看全部</el-button>
                    </div>
                    <el-table :data="recentLogs" style="width: 100%" size="small">
                        <el-table-column prop="time" label="时间" width="180">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.time) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="user" label="操作用户" width="120"></el-table-column>
                        <el-table-column prop="action" label="操作类型" width="120">
                            <template #default="scope">
                                <el-tag :type="getLogType(scope.row.action)" size="mini">
                                    {{ scope.row.action }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="description" label="操作描述" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
                    </el-table>
                </div>
            </div>
        </main>
    </div>
    <!-- 页脚 -->
    <footer class="bg-white py-4 border-t border-neutral-200">
        <div class="container mx-auto px-4 text-center text-neutral-500 text-sm">
            <p>© 2025 息壤集后台管理系统 | 自然美肌，源于匠心</p>
        </div>
    </footer>
    <!-- 新增用户对话框 -->
    <el-dialog :visible.sync="addUserDialogVisible" title="新增用户">
        <template #default>
            <el-form :model="newUser" label-width="100px" ref="userForm" >
                <el-form-item label="用户ID" prop="userId" for="new-user-id">
                    <el-input
                        id="new-user-id"
                        name="newUserId"
                        v-model="newUser.userId"
                        autocomplete="username">
                    </el-input>
                </el-form-item>
                <el-form-item label="用户名" prop="username" for="new-user-name">
                    <el-input
                        id="new-user-name"
                        name="newUserName"
                        v-model="newUser.username"
                        autocomplete="name">
                    </el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone" for="new-user-phone">
                    <el-input
                        id="new-user-phone"
                        name="newUserPhone"
                        v-model="newUser.phone"
                        autocomplete="tel">
                    </el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password" for="new-user-password">
                    <el-input
                        id="new-user-password"
                        name="newUserPassword"
                        v-model="newUser.password"
                        type="password"
                        autocomplete="new-password">
                    </el-input>
                </el-form-item>
                <el-form-item label="注册时间" prop="registerTime" for="new-user-register-time">
                    <el-input
                        id="new-user-register-time"
                        name="newUserRegisterTime"
                        v-model="formattedNewUserRegisterTime"
                        disabled
                        placeholder="注册时间"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <el-button @click="addUserDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="addUser">确定</el-button>
        </template>
    </el-dialog>
    <!-- 新增产品对话框 - 多图片版本 -->
    <el-dialog :visible.sync="addProductDialogVisible" title="新增产品" width="90%" top="2vh" :close-on-click-modal="false" custom-class="product-dialog">
        <template #default>
            <el-form :model="newProduct" label-width="120px" ref="productForm">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品名称" prop="name" for="new-product-name" :rules="[{ required: true, message: '请输入产品名称', trigger: 'blur' }]">
                            <el-input
                                id="new-product-name"
                                name="productName"
                                v-model="newProduct.name"
                                placeholder="请输入产品名称"
                                autocomplete="off">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="产品价格" prop="price" for="new-product-price" :rules="[{ required: true, message: '请输入产品价格', trigger: 'blur' }]">
                            <el-input
                                id="new-product-price"
                                name="productPrice"
                                v-model.number="newProduct.price"
                                type="number"
                                placeholder="请输入价格"
                                autocomplete="off">
                                <template slot="prepend">¥</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="产品描述" prop="description" for="new-product-description">
                    <el-input
                        id="new-product-description"
                        name="productDescription"
                        type="textarea"
                        v-model="newProduct.description"
                        rows="3"
                        placeholder="请输入产品描述"
                        autocomplete="off">
                    </el-input>
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="库存数量" prop="stock" for="new-product-stock" :rules="[{ required: true, message: '请输入库存数量', trigger: 'blur' }]">
                            <el-input
                                id="new-product-stock"
                                name="productStock"
                                v-model.number="newProduct.stock"
                                type="number"
                                placeholder="请输入库存数量"
                                autocomplete="off">
                                <template slot="append">件</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否新品" prop="isNew" for="new-product-is-new">
                            <el-switch
                                id="new-product-is-new"
                                name="productIsNew"
                                v-model="newProduct.isNew"
                                active-text="是"
                                inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属分类" prop="categoryIds" for="new-product-categories">
                            <el-select
                                id="new-product-categories"
                                name="productCategories"
                                v-model="newProduct.categoryIds"
                                multiple
                                placeholder="请选择分类"
                                style="width: 100%;"
                                autocomplete="off">
                                <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 多图片管理区域 -->
                <el-form-item label="产品图片" style="margin-top: 20px;">
                    <div class="multi-image-manager" style="width: 100%; border: 1px solid #e4e7ed; border-radius: 8px; padding: 15px; background: #f8fafc;">
                        <!-- 头部信息 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #e4e7ed;">
                            <div>
                                <h4 style="margin: 0; color: #409eff; font-size: 16px; font-weight: bold;">
                                    📷 图片管理
                                </h4>
                                <p style="margin: 5px 0 0 0; color: #909399; font-size: 12px;">
                                    第一张图片将自动设为封面
                                </p>
                            </div>
                            <div>
                                <el-tag type="info" size="small">
                                    共 {{ newProductImages.length }} 张
                                </el-tag>
                            </div>
                        </div>

                        <!-- 已选择的图片展示 -->
                        <div v-if="newProductImages.length > 0" style="margin-bottom: 15px;">
                            <h5 style="margin: 0 0 10px 0; color: #606266; font-size: 14px; font-weight: bold;">
                                📸 已选择的图片:
                            </h5>
                            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                <div v-for="(image, index) in newProductImages" :key="index"
                                     class="new-image-card"
                                     style="position: relative; width: 120px; height: 120px; border: 2px solid #e4e7ed; border-radius: 4px; overflow: hidden; background: white; transition: all 0.3s ease; cursor: pointer;"
                                     @mouseenter="$event.target.classList.add('image-card-hover')"
                                     @mouseleave="$event.target.classList.remove('image-card-hover')">

                                    <img :src="image.imageUrl" :alt="image.imageName"
                                         style="width: 100%; height: 100%; object-fit: contain;">

                                    <!-- 封面标识 - 左上角皇冠图标 -->
                                    <div v-if="index === 0"
                                         class="image-cover-badge"
                                         style="position: absolute; top: 6px; left: 6px; background: linear-gradient(45deg, #FFD700, #FFA500); color: white; padding: 3px 8px; border-radius: 8px; font-size: 10px; font-weight: bold; box-shadow: 0 2px 4px rgba(255, 215, 0, 0.4); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="el-icon-medal" style="font-size: 10px; margin-right: 2px;"></i>封面
                                    </div>

                                    <!-- 删除按钮 - 右上角红色圆圈 -->
                                    <div @click="deleteNewProductImage(index)"
                                         class="image-delete-btn"
                                         style="position: absolute; top: 6px; right: 6px; width: 20px; height: 20px; background: #ff4757; border: 2px solid white; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white; font-size: 12px; font-weight: bold; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4);"
                                         @mouseenter="$event.target.classList.add('image-delete-hover')"
                                         @mouseleave="$event.target.classList.remove('image-delete-hover')"
                                         title="删除这张图片">
                                        <i class="el-icon-close"></i>
                                    </div>

                                    <!-- 图片序号 - 右下角圆形数字 -->
                                    <div class="image-sequence-badge"
                                         style="position: absolute; bottom: 6px; right: 6px; width: 24px; height: 24px; background: rgba(64, 158, 255, 0.9); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
                                        {{ index + 1 }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 上传区域 -->
                        <div>
                            <h5 style="margin: 0 0 10px 0; color: #606266; font-size: 14px; font-weight: bold;">
                                📤 选择图片:
                            </h5>
                            <el-upload
                                class="multi-image-uploader"
                                action="#"
                                :show-file-list="false"
                                :on-change="handleNewProductImageChange"
                                :before-upload="() => false"
                                multiple
                                accept=".jpg,.jpeg,.png"
                                drag>
                                <div class="upload-drag-area" style="padding: 30px; text-align: center;">
                                    <i class="el-icon-upload" style="font-size: 36px; color: #c0c4cc; margin-bottom: 10px;"></i>
                                    <div style="color: #606266; font-size: 14px; margin-bottom: 8px;">
                                        拖拽或<em style="color: #409eff; font-weight: bold;">点击选择图片</em>
                                    </div>
                                    <div style="color: #909399; font-size: 12px;">
                                        支持 JPG、PNG 格式，第一张自动设为封面
                                    </div>
                                </div>
                            </el-upload>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <div style="text-align: right;">
                <el-button @click="addProductDialogVisible = false" size="medium">取消</el-button>
                <el-button type="primary" @click="addProduct" size="medium">
                    <i class="el-icon-check"></i> 确定新增
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 新增分类对话框 -->
    <el-dialog :visible.sync="addCategoryDialogVisible" title="新增分类">
        <template #default>
            <el-form :model="newCategory" label-width="100px" ref="categoryForm">
                <el-form-item label="分类名称" prop="name" for="new-category-name">
                    <el-input
                        id="new-category-name"
                        name="newCategoryName"
                        v-model="newCategory.name"
                        placeholder="请输入分类名称"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <el-button @click="addCategoryDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="addCategory">确定</el-button>
        </template>
    </el-dialog>

    <!-- 编辑产品对话框 - 多图片版本 -->
    <el-dialog :visible.sync="editProductDialogVisible" title="编辑产品" width="90%" top="2vh" :close-on-click-modal="false" custom-class="product-dialog">
        <template #default>
            <el-form :model="editProductForm" label-width="120px" ref="editProductForm">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品名称" prop="name" for="edit-product-name" :rules="[{ required: true, message: '请输入产品名称', trigger: 'blur' }]">
                            <el-input
                                id="edit-product-name"
                                name="editProductName"
                                v-model="editProductForm.name"
                                placeholder="请输入产品名称"
                                autocomplete="off">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="产品价格" prop="price" for="edit-product-price" :rules="[{ required: true, message: '请输入产品价格', trigger: 'blur' }]">
                            <el-input
                                id="edit-product-price"
                                name="editProductPrice"
                                v-model.number="editProductForm.price"
                                type="number"
                                placeholder="请输入价格"
                                autocomplete="off">
                                <template slot="prepend">¥</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="产品描述" prop="description" for="edit-product-description">
                    <el-input
                        id="edit-product-description"
                        name="editProductDescription"
                        type="textarea"
                        v-model="editProductForm.description"
                        rows="3"
                        placeholder="请输入产品描述"
                        autocomplete="off">
                    </el-input>
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="库存数量" prop="stock" for="edit-product-stock" :rules="[{ required: true, message: '请输入库存数量', trigger: 'blur' }]">
                            <el-input
                                id="edit-product-stock"
                                name="editProductStock"
                                v-model.number="editProductForm.stock"
                                type="number"
                                placeholder="请输入库存数量"
                                autocomplete="off">
                                <template slot="append">件</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否新品" prop="isNew" for="edit-product-is-new">
                            <el-switch
                                id="edit-product-is-new"
                                name="editProductIsNew"
                                v-model="editProductForm.isNew"
                                active-text="是"
                                inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属分类" prop="categoryIds" for="edit-product-categories">
                            <el-select
                                id="edit-product-categories"
                                name="editProductCategories"
                                v-model="editProductForm.categoryIds"
                                multiple
                                placeholder="请选择分类"
                                style="width: 100%;"
                                autocomplete="off">
                                <el-option v-for="category in categoryList" :key="category.id" :label="category.name" :value="category.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 多图片管理区域 -->
                <el-form-item label="产品图片" style="margin-top: 30px;">
                    <div class="multi-image-manager" style="width: 100%; border: 2px solid #e4e7ed; border-radius: 12px; padding: 30px; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
                        <!-- 头部信息和操作 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 25px; border-bottom: 2px solid #e4e7ed;">
                            <div>
                                <h4 style="margin: 0; color: #409eff; font-size: 20px; font-weight: bold;">
                                    <i class="el-icon-picture-outline"></i> 多图片管理
                                </h4>
                                <p style="margin: 10px 0 0 0; color: #909399; font-size: 15px;">
                                    管理产品的多张图片，支持删除、设置封面和新增图片
                                </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <el-button size="small" type="primary" @click="loadEditProductImages" icon="el-icon-refresh">
                                    刷新图片
                                </el-button>
                                <el-tag type="info" size="medium">
                                    共 {{ editProductImages.length }} 张图片
                                </el-tag>
                            </div>
                        </div>

                        <!-- 现有图片展示 -->
                        <div v-if="editProductImages.length > 0" style="margin-bottom: 35px;">
                            <h5 style="margin: 0 0 25px 0; color: #606266; font-size: 18px; font-weight: bold;">
                                <i class="el-icon-view"></i> 现有图片:
                            </h5>
                            <div style="display: flex; flex-wrap: wrap; gap: 25px;">
                                <div v-for="(image, index) in editProductImages" :key="image.id"
                                     class="edit-image-card"
                                     style="position: relative; width: 180px; height: 180px; border: 3px solid #e4e7ed; border-radius: 6px; overflow: hidden; background: white; transition: all 0.3s ease; cursor: pointer;"
                                     @mouseenter="$event.target.classList.add('image-card-hover')"
                                     @mouseleave="$event.target.classList.remove('image-card-hover')">

                                    <img :src="image.imageUrl" :alt="image.imageName || '产品图片'"
                                         style="width: 100%; height: 100%; object-fit: contain;">

                                    <!-- 封面标识 - 左上角皇冠图标 -->
                                    <div v-if="image.isPrimary"
                                         class="image-cover-badge"
                                         style="position: absolute; top: 6px; left: 6px; background: linear-gradient(45deg, #FFD700, #FFA500); color: white; padding: 3px 8px; border-radius: 8px; font-size: 10px; font-weight: bold; box-shadow: 0 2px 4px rgba(255, 215, 0, 0.4); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="el-icon-medal" style="font-size: 10px; margin-right: 2px;"></i>封面
                                    </div>

                                    <!-- 删除按钮 - 右上角红色圆圈 -->
                                    <div @click="deleteEditProductImage(image.id)"
                                         class="image-delete-btn"
                                         style="position: absolute; top: 6px; right: 6px; width: 20px; height: 20px; background: #ff4757; border: 2px solid white; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white; font-size: 12px; font-weight: bold; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4);"
                                         @mouseenter="$event.target.classList.add('image-delete-hover')"
                                         @mouseleave="$event.target.classList.remove('image-delete-hover')"
                                         title="删除这张图片">
                                        <i class="el-icon-close"></i>
                                    </div>

                                    <!-- 设为封面按钮 - 左下角放大 -->
                                    <div v-if="!image.isPrimary" @click="setEditProductPrimaryImage(image.id)"
                                         class="image-primary-btn"
                                         style="position: absolute; bottom: 6px; left: 6px; width: 24px; height: 24px; background: rgba(64, 158, 255, 0.9); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white; font-size: 12px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(64, 158, 255, 0.4); border: 2px solid white;"
                                         @mouseenter="$event.target.classList.add('image-primary-hover')"
                                         @mouseleave="$event.target.classList.remove('image-primary-hover')"
                                         title="设为封面图">
                                        <i class="el-icon-star-off"></i>
                                    </div>

                                    <!-- 图片序号 - 右下角圆形数字 -->
                                    <div class="image-sequence-badge"
                                         style="position: absolute; bottom: 6px; right: 6px; width: 24px; height: 24px; background: rgba(64, 158, 255, 0.9); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
                                        {{ index + 1 }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 无图片提示 -->
                        <div v-else style="text-align: center; padding: 50px; color: #909399; background: white; border-radius: 12px; margin-bottom: 35px;">
                            <i class="el-icon-picture-outline" style="font-size: 80px; margin-bottom: 20px;"></i>
                            <p style="margin: 0; font-size: 18px;">暂无图片，请上传产品图片</p>
                        </div>

                        <!-- 上传新图片区域 -->
                        <div>
                            <h5 style="margin: 0 0 25px 0; color: #606266; font-size: 18px; font-weight: bold;">
                                <i class="el-icon-upload"></i> 上传新图片:
                            </h5>
                            <el-upload
                                class="multi-image-uploader"
                                :action="'/api/product-images/upload-multiple'"
                                :show-file-list="false"
                                :on-success="handleEditProductMultiUploadSuccess"
                                :on-error="handleEditProductMultiUploadError"
                                :before-upload="beforeEditProductMultiUpload"
                                :data="{ productId: editProductForm.id }"
                                name="files"
                                multiple
                                accept=".jpg,.jpeg,.png"
                                drag>
                                <div class="upload-drag-area" style="padding: 70px; text-align: center;">
                                    <i class="el-icon-upload" style="font-size: 64px; color: #c0c4cc; margin-bottom: 25px;"></i>
                                    <div style="color: #606266; font-size: 20px; margin-bottom: 15px;">
                                        将图片拖到此处，或<em style="color: #409eff; font-weight: bold;">点击选择多张图片</em>
                                    </div>
                                    <div style="color: #909399; font-size: 15px;">
                                        支持 JPG、PNG 格式，单张图片不超过 2MB<br>
                                        可以同时选择多张图片进行上传
                                    </div>
                                </div>
                            </el-upload>
                        </div>

                        <!-- 操作说明 -->
                        <div style="margin-top: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                            <h6 style="margin: 0 0 20px 0; font-size: 18px; font-weight: bold;">
                                <i class="el-icon-info"></i> 操作说明:
                            </h6>
                            <ul style="margin: 0; padding-left: 30px; font-size: 15px; line-height: 2;">
                                <li><strong>删除图片</strong>: 点击图片右上角的红色圆形 <strong>×</strong> 按钮（20px大小，悬停放大并旋转180度）</li>
                                <li><strong>设置封面</strong>: 点击图片左下角的蓝色圆形 <strong>⭐</strong> 按钮（24px大小，悬停放大并旋转360度）</li>
                                <li><strong>图片序号</strong>: 右下角蓝色圆形数字标识（24px圆形徽章显示图片顺序）</li>
                                <li><strong>封面标识</strong>: 左上角金色奖牌 <strong>🏅</strong> 图标标识当前封面图片</li>
                                <li><strong>图片显示</strong>: 完整比例显示，6px圆角边框，正常照片尺寸</li>
                                <li><strong>上传图片</strong>: 支持拖拽上传和点击选择多张图片</li>
                                <li><strong>图片要求</strong>: JPG、PNG 格式，单张不超过 2MB</li>
                                <li><strong>自动封面</strong>: 如果没有封面图，第一张图片会自动设为封面</li>
                            </ul>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <div style="text-align: right;">
                <el-button @click="editProductDialogVisible = false" size="medium">取消</el-button>
                <el-button type="primary" @click="saveProduct" size="medium">
                    <i class="el-icon-check"></i> 保存修改
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 新增轮播图对话框 -->
    <el-dialog :visible.sync="addCarouselDialogVisible" title="新增轮播图" width="60%">
        <template #default>
            <el-form :model="newCarousel" label-width="120px" ref="addCarouselForm">
                <el-form-item label="轮播图标题" prop="title" for="add-carousel-title">
                    <el-input
                        id="add-carousel-title"
                        name="addCarouselTitle"
                        v-model="newCarousel.title"
                        placeholder="请输入轮播图标题"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item label="轮播图描述" prop="description" for="add-carousel-description">
                    <el-input
                        id="add-carousel-description"
                        name="addCarouselDescription"
                        type="textarea"
                        :rows="3"
                        v-model="newCarousel.description"
                        placeholder="请输入轮播图描述"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item label="关联分类" prop="categoryId" for="add-carousel-category">
                    <el-select
                        id="add-carousel-category"
                        name="addCarouselCategory"
                        v-model="newCarousel.categoryId"
                        placeholder="请选择关联分类"
                        style="width: 100%">
                        <el-option
                            v-for="category in categoryList"
                            :key="category.id"
                            :label="category.name"
                            :value="category.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="排序权重" prop="sortOrder" for="add-carousel-sort">
                    <el-input-number
                        id="add-carousel-sort"
                        name="addCarouselSort"
                        v-model="newCarousel.sortOrder"
                        :min="0"
                        :max="999"
                        placeholder="排序权重">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="轮播图片" prop="imageUrl">
                    <el-upload
                        class="carousel-uploader"
                        :action="'/carousel/upload'"
                        :show-file-list="false"
                        :on-success="handleCarouselUploadSuccess"
                        :before-upload="beforeCarouselUpload"
                        accept=".jpg,.jpeg,.png">
                        <img v-if="newCarousel.imageUrl" :src="newCarousel.imageUrl" class="carousel-image">
                        <i v-else class="el-icon-plus carousel-uploader-icon"></i>
                    </el-upload>
                    <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <div style="text-align: right;">
                <el-button @click="addCarouselDialogVisible = false" size="medium">取消</el-button>
                <el-button type="primary" @click="addCarousel" size="medium">
                    <i class="el-icon-check"></i> 确定添加
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 编辑轮播图对话框 -->
    <el-dialog :visible.sync="editCarouselDialogVisible" title="编辑轮播图" width="60%">
        <template #default>
            <el-form :model="editCarouselForm" label-width="120px" ref="editCarouselForm">
                <el-form-item label="轮播图标题" prop="title" for="edit-carousel-title">
                    <el-input
                        id="edit-carousel-title"
                        name="editCarouselTitle"
                        v-model="editCarouselForm.title"
                        placeholder="请输入轮播图标题"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item label="轮播图描述" prop="description" for="edit-carousel-description">
                    <el-input
                        id="edit-carousel-description"
                        name="editCarouselDescription"
                        type="textarea"
                        :rows="3"
                        v-model="editCarouselForm.description"
                        placeholder="请输入轮播图描述"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item label="关联分类" prop="categoryId" for="edit-carousel-category">
                    <el-select
                        id="edit-carousel-category"
                        name="editCarouselCategory"
                        v-model="editCarouselForm.categoryId"
                        placeholder="请选择关联分类"
                        style="width: 100%">
                        <el-option
                            v-for="category in categoryList"
                            :key="category.id"
                            :label="category.name"
                            :value="category.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="排序权重" prop="sortOrder" for="edit-carousel-sort">
                    <el-input-number
                        id="edit-carousel-sort"
                        name="editCarouselSort"
                        v-model="editCarouselForm.sortOrder"
                        :min="0"
                        :max="999"
                        placeholder="排序权重">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="轮播图片" prop="imageUrl">
                    <el-upload
                        class="carousel-uploader"
                        :action="'/carousel/upload'"
                        :show-file-list="false"
                        :on-success="handleEditCarouselUploadSuccess"
                        :before-upload="beforeCarouselUpload"
                        accept=".jpg,.jpeg,.png">
                        <img v-if="editCarouselForm.imageUrl" :src="editCarouselForm.imageUrl" class="carousel-image">
                        <i v-else class="el-icon-plus carousel-uploader-icon"></i>
                    </el-upload>
                    <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <div style="text-align: right;">
                <el-button @click="editCarouselDialogVisible = false" size="medium">取消</el-button>
                <el-button type="primary" @click="saveCarousel" size="medium">
                    <i class="el-icon-check"></i> 保存修改
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 编辑分类对话框 -->
    <el-dialog :visible.sync="editCategoryDialogVisible" title="编辑分类">
        <template #default>
            <el-form :model="editCategoryForm" label-width="100px" ref="editCategoryForm">
                <el-form-item label="分类名称" prop="name" for="edit-category-name">
                    <el-input
                        id="edit-category-name"
                        name="editCategoryName"
                        v-model="editCategoryForm.name"
                        placeholder="请输入分类名称"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <el-button @click="editCategoryDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveCategory">确定</el-button>
        </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog :visible.sync="userDetailDialogVisible" title="用户详情" width="80%" top="5vh">
        <template #default>
            <div v-if="currentUserDetail" class="user-detail-content">
                <!-- 基本信息卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800">基本信息</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <img :src="getAvatarUrl(currentUserDetail.avatarUrl)"
                                     alt="用户头像" class="w-12 h-12 rounded-full mr-3 object-cover"
                                     @error="handleAvatarError">
                                <div>
                                    <p class="font-medium">{{ currentUserDetail.username }}</p>
                                    <p class="text-sm text-gray-600">{{ currentUserDetail.userId }}</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">手机号：</span>
                                    <span class="font-medium">{{ currentUserDetail.phone }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">注册时间：</span>
                                    <span class="font-medium">{{ formatDateTime(currentUserDetail.registerTime) }}</span>
                                </div>
                            </div>
                            <div v-if="currentUserDetail.signature">
                                <span class="text-gray-600">个性签名：</span>
                                <p class="text-sm italic text-gray-700 mt-1">{{ currentUserDetail.signature }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息卡片 -->
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800">统计信息</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-blue-600">{{ currentUserDetail.cartItemCount }}</div>
                                <div class="text-sm text-gray-600">购物车商品</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-green-600">{{ currentUserDetail.totalOrderCount }}</div>
                                <div class="text-sm text-gray-600">总订单数</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-orange-600">{{ currentUserDetail.pendingOrderCount }}</div>
                                <div class="text-sm text-gray-600">待处理订单</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-purple-600">{{ currentUserDetail.friendCount }}</div>
                                <div class="text-sm text-gray-600">好友数量</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收货地址信息 -->
                <div class="bg-white p-6 rounded-lg border mb-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">收货地址</h3>
                    <div v-if="currentUserDetail.addresses && currentUserDetail.addresses.length > 0">
                        <div v-for="(address, index) in currentUserDetail.addresses" :key="index"
                             class="border rounded-lg p-4 mb-3 last:mb-0"
                             :class="address.isDefault ? 'border-blue-300 bg-blue-50' : 'border-gray-200'">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="font-medium">{{ address.receiverName }}</span>
                                        <span class="ml-2 text-gray-600">{{ address.receiverPhone }}</span>
                                        <el-tag v-if="address.isDefault" type="primary" size="mini" class="ml-2">默认</el-tag>
                                    </div>
                                    <p class="text-gray-700 text-sm">{{ address.fullAddress || (address.province + address.city + address.district + address.detailAddress) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-8 text-gray-500">
                        <i class="fa fa-map-marker text-3xl mb-2"></i>
                        <p>暂无收货地址</p>
                    </div>
                </div>
            </div>
            <div v-else class="text-center py-8">
                <i class="fa fa-spinner fa-spin text-2xl text-gray-400"></i>
                <p class="text-gray-500 mt-2">加载中...</p>
            </div>
        </template>
        <template #footer>
            <el-button @click="userDetailDialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog :visible.sync="dialogVisible" title="编辑用户">
        <template #default>
            <el-form :model="editForm" label-width="100px" ref="editUserForm">
                <el-form-item label="用户ID" prop="userId" for="edit-user-id">
                    <el-input
                        id="edit-user-id"
                        name="editUserId"
                        v-model="editForm.userId"
                        disabled
                        autocomplete="username">
                    </el-input>
                </el-form-item>
                <el-form-item label="用户名" prop="username" for="edit-user-name">
                    <el-input
                        id="edit-user-name"
                        name="editUserName"
                        v-model="editForm.username"
                        autocomplete="name">
                    </el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone" for="edit-user-phone">
                    <el-input
                        id="edit-user-phone"
                        name="editUserPhone"
                        v-model="editForm.phone"
                        autocomplete="tel">
                    </el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password" for="edit-user-password">
                    <el-input
                        id="edit-user-password"
                        name="editUserPassword"
                        v-model="editForm.password"
                        :type="showPassword ? 'text' : 'password'"
                        placeholder="请输入密码"
                        autocomplete="current-password">
                        <template #suffix>
                            <el-button
                                type="text"
                                @click="showPassword = !showPassword"
                                style="padding: 0; margin-right: 8px;">
                                <i :class="showPassword ? 'el-icon-view' : 'el-icon-view-off'"></i>
                            </el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="注册时间" prop="registerTime" for="edit-user-register-time">
                    <el-input
                        id="edit-user-register-time"
                        name="editUserRegisterTime"
                        v-model="formattedRegisterTime"
                        disabled
                        placeholder="注册时间"
                        autocomplete="off">
                    </el-input>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveUser">确定</el-button>
        </template>
    </el-dialog>

    <!-- 产品详情对话框 -->
    <el-dialog title="产品详情" :visible.sync="productDetailDialogVisible" width="800px" class="product-detail-dialog">
        <div v-if="currentProductDetail" class="product-detail-content">
            <div class="flex gap-6">
                <!-- 产品图片区域 -->
                <div class="w-1/2">
                    <div class="product-images">
                        <div class="main-image mb-4">
                            <img :src="getImageUrl(currentProductDetail.primary_image_url || currentProductDetail.imageUrl)"
                                 alt="产品主图"
                                 class="w-full h-64 object-cover rounded-lg"
                                 @error="handleImageError">
                        </div>
                        <div class="image-gallery-container mt-4">
                            <div v-if="currentProductDetail.images && currentProductDetail.images.length > 0" class="image-gallery">
                                <div class="gallery-header mb-3">
                                    <span class="text-sm text-gray-600">
                                        <i class="el-icon-picture mr-1"></i>
                                        产品图片 ({{ currentProductDetail.images.length }})
                                    </span>
                                </div>
                                <div class="gallery-grid" style="max-height: 200px; overflow-y: auto;">
                                    <div class="grid grid-cols-3 gap-3">
                                        <div v-for="(image, index) in currentProductDetail.images"
                                             :key="index"
                                             class="image-item relative group">
                                            <img :src="getImageUrl(image.image_url || image.imageUrl)"
                                                 :alt="'产品图片' + (index + 1)"
                                                 class="w-full h-20 object-cover rounded-lg cursor-pointer transition-all duration-200 hover:scale-105 border-2 border-gray-200 hover:border-blue-400"
                                                 @click="changeMainImage(image.image_url || image.imageUrl)"
                                                 @error="handleImageError">
                                            <div class="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                                                {{ index + 1 }}
                                            </div>
                                            <div v-if="image.is_primary" class="absolute top-1 left-1 bg-red-500 text-white text-xs px-1 rounded">
                                                主图
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="no-images text-center py-6 text-gray-400">
                                <i class="el-icon-picture text-3xl mb-2 block"></i>
                                <span>暂无产品图片</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 产品信息区域 -->
                <div class="w-1/2">
                    <div class="product-info space-y-4">
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">{{ currentProductDetail.name }}</h3>
                            <p class="text-gray-600 mt-2">{{ currentProductDetail.description }}</p>
                        </div>

                        <div class="price-stock flex gap-4">
                            <div class="price">
                                <span class="text-sm text-gray-500">价格</span>
                                <div class="text-2xl font-bold text-red-500">¥{{ currentProductDetail.price }}</div>
                            </div>
                            <div class="stock">
                                <span class="text-sm text-gray-500">库存</span>
                                <div class="text-xl font-semibold" :class="currentProductDetail.stock > 50 ? 'text-green-500' : currentProductDetail.stock > 20 ? 'text-yellow-500' : 'text-red-500'">
                                    {{ currentProductDetail.stock }}
                                </div>
                            </div>
                        </div>

                        <div class="product-tags flex gap-2">
                            <el-tag v-if="currentProductDetail.isNew" type="success" size="small">新品</el-tag>
                            <el-tag :type="currentProductDetail.status === 1 ? 'success' : 'danger'" size="small">
                                {{ currentProductDetail.status === 1 ? '上架中' : '已下架' }}
                            </el-tag>
                        </div>

                        <div class="product-categories" v-if="currentProductDetail.categories && currentProductDetail.categories.length > 0">
                            <span class="text-sm text-gray-500">分类：</span>
                            <el-tag v-for="category in currentProductDetail.categories" :key="category.id" size="mini" class="ml-1">
                                {{ category.name }}
                            </el-tag>
                        </div>

                        <div class="product-stats grid grid-cols-2 gap-4 mt-6">
                            <div class="stat-item bg-gray-50 p-3 rounded">
                                <div class="text-sm text-gray-500">创建时间</div>
                                <div class="font-semibold">{{ formatDateTime(currentProductDetail.createTime) }}</div>
                            </div>
                            <div class="stat-item bg-gray-50 p-3 rounded">
                                <div class="text-sm text-gray-500">更新时间</div>
                                <div class="font-semibold">{{ formatDateTime(currentProductDetail.updateTime) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="productDetailDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="editProduct(currentProductDetail)">编辑产品</el-button>
        </div>
    </el-dialog>

    <!-- 产品图片管理对话框 -->
    <el-dialog :title="`${currentProductForImages ? currentProductForImages.name : ''} - 图片管理`"
               :visible.sync="productImageDialogVisible"
               width="950px"
               class="product-image-dialog"
               :show-close="true"
               :close-on-click-modal="false">
        <div v-if="currentProductForImages" class="image-management">
            <!-- 提示信息 -->
            <div class="info-header mb-6 p-4 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <i class="el-icon-camera text-blue-500 mr-2"></i>
                    <span class="text-gray-700">您可以上传多张图片，设置主图显示。支持 JPG、PNG 格式，单个文件不超过 2MB</span>
                </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="upload-section mb-8">
                <div class="section-header mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="el-icon-upload2 mr-2 text-blue-500"></i>
                        上传新图片
                    </h3>
                </div>
                <el-upload
                    class="image-uploader"
                    :action="'/api/product-images/upload'"
                    :data="{productId: currentProductForImages.id}"
                    :show-file-list="false"
                    :on-success="handleImageUploadSuccess"
                    :before-upload="beforeImageUpload"
                    accept="image/*"
                    multiple>
                    <div class="upload-area border-3 border-dashed border-blue-200 rounded-xl p-10 text-center hover:border-blue-400 hover:bg-blue-50 cursor-pointer transition-all duration-300">
                        <div class="upload-icon mb-4">
                            <i class="el-icon-plus text-5xl text-blue-400"></i>
                        </div>
                        <div class="upload-text">
                            <div class="text-lg font-medium text-gray-700 mb-2">点击上传图片</div>
                            <div class="text-sm text-gray-500">
                                <i class="el-icon-info mr-1"></i>
                                支持 JPG、PNG 格式，单个文件不超过 2MB，可同时选择多张图片
                            </div>
                        </div>
                    </div>
                </el-upload>
            </div>

            <!-- 现有图片列表 -->
            <div class="existing-images">
                <div class="section-header mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center justify-between">
                        <span class="flex items-center">
                            <i class="el-icon-picture-outline mr-2 text-green-500"></i>
                            现有图片
                        </span>
                        <span class="text-sm font-normal bg-green-100 text-green-700 px-3 py-1 rounded-full">
                            {{ productImages.length }} 张图片
                        </span>
                    </h3>
                </div>

                <div v-if="productImages.length === 0" class="no-images text-center py-12 bg-gray-50 rounded-xl">
                    <div class="no-images-icon mb-4">
                        <i class="el-icon-picture text-6xl text-gray-300"></i>
                    </div>
                    <div class="no-images-text">
                        <p class="text-lg text-gray-500 mb-2">暂无图片</p>
                        <p class="text-sm text-gray-400">请点击上方区域上传产品图片</p>
                    </div>
                </div>

                <div v-else class="image-grid">
                    <div class="grid grid-cols-3 gap-4" style="max-height: 450px; overflow-y: auto; padding: 10px;">
                        <div v-for="(image, index) in productImages" :key="image.id" class="image-item">
                            <div class="image-card bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden">
                                <div class="image-container relative">
                                    <img :src="getImageUrl(image.image_url || image.imageUrl)"
                                         alt="产品图片"
                                         class="w-full h-40 object-cover"
                                         @error="handleImageError">

                                    <!-- 主图标识 -->
                                    <div v-if="image.is_primary" class="absolute top-3 left-3">
                                        <div class="primary-badge bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
                                            <i class="el-icon-star-on mr-1"></i>
                                            主图
                                        </div>
                                    </div>

                                    <!-- 图片序号 -->
                                    <div class="absolute top-3 right-3">
                                        <div class="image-number bg-black bg-opacity-60 text-white w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                                            {{ index + 1 }}
                                        </div>
                                    </div>

                                    <!-- 设为主图按钮 - 常显 -->
                                    <div v-if="!image.is_primary" class="absolute bottom-3 right-3">
                                        <el-tooltip content="点击设为主图" placement="top">
                                            <el-button size="mini" type="warning" @click="setPrimaryImage(image)" circle style="box-shadow: 0 2px 8px rgba(0,0,0,0.3); background: #f39c12; border-color: #f39c12;">
                                                <i class="el-icon-star-off" style="color: white;"></i>
                                            </el-button>
                                        </el-tooltip>
                                    </div>

                                    <!-- 操作按钮遮罩 - 只覆盖图片区域，不覆盖底部信息 -->
                                    <div class="absolute top-0 left-0 right-0 bottom-0 bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center" style="z-index: 20; pointer-events: none;">
                                        <!-- 操作按钮容器 -->
                                        <div class="flex gap-2" style="pointer-events: auto;">
                                            <el-tooltip v-if="!image.is_primary" content="设为主图" placement="top">
                                                <el-button size="mini" type="warning" @click="setPrimaryImage(image)" circle style="box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                                                    <i class="el-icon-star-off"></i>
                                                </el-button>
                                            </el-tooltip>
                                            <el-tooltip content="预览图片" placement="top">
                                                <el-button size="mini" type="primary" @click="previewImage(getImageUrl(image.image_url || image.imageUrl))" circle style="box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                                                    <i class="el-icon-view"></i>
                                                </el-button>
                                            </el-tooltip>
                                            <el-tooltip content="删除图片" placement="top">
                                                <el-button size="mini" type="danger" @click="deleteProductImage(image)" circle style="box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                                                    <i class="el-icon-delete"></i>
                                                </el-button>
                                            </el-tooltip>
                                        </div>
                                    </div>
                                </div>

                                <!-- 图片信息 -->
                                <div class="image-info p-3 bg-white border-t border-gray-100" style="position: relative; z-index: 10;">
                                    <div class="text-xs text-gray-600 text-center">
                                        <div class="mb-1 font-medium">
                                            <i class="el-icon-document mr-1 text-blue-500"></i>
                                            {{ formatFileSize(image.file_size) }}
                                        </div>
                                        <div class="text-gray-500 truncate" :title="image.image_name || '图片' + (index + 1)">
                                            {{ image.image_name || '图片' + (index + 1) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer pt-6 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <i class="el-icon-info mr-1"></i>
                    提示：第一张图片将作为产品主图显示，您也可以手动设置主图
                </div>
                <div class="action-buttons">
                    <el-button @click="productImageDialogVisible = false" size="medium">
                        <i class="el-icon-close mr-1"></i>关闭
                    </el-button>
                    <el-button type="primary" @click="refreshProductImages" size="medium">
                        <i class="el-icon-refresh mr-1"></i>刷新
                    </el-button>
                </div>
            </div>
        </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewDialogVisible" width="70%" top="5vh">
        <div class="text-center" style="max-height: 70vh; overflow: hidden;">
            <img :src="previewImageUrl" alt="预览图片"
                 style="max-width: 100%; max-height: 65vh; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="imagePreviewDialogVisible = false" type="primary">关闭</el-button>
        </div>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog :visible.sync="orderDetailDialogVisible" title="订单详情" width="80%" top="5vh">
        <template #default>
            <div v-if="currentOrderDetail" class="order-detail-content">
                <!-- 订单基本信息 -->
                <div class="order-basic-info mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">📋 订单基本信息</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">订单号</label>
                            <p class="text-sm text-gray-900 font-mono">{{ currentOrderDetail.orderNo }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">订单状态</label>
                            <el-tag :type="getOrderStatusType(currentOrderDetail.status)">
                                {{ getOrderStatusText(currentOrderDetail.status) }}
                            </el-tag>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">订单金额</label>
                            <p class="text-sm text-red-600 font-bold">¥{{ currentOrderDetail.totalAmount }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">下单时间</label>
                            <p class="text-sm text-gray-900">{{ formatTime(currentOrderDetail.createdTime) }}</p>
                        </div>
                    </div>
                </div>

                <!-- 收货信息 -->
                <div class="shipping-info mb-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">🚚 收货信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">收货人</label>
                            <p class="text-sm text-gray-900">{{ currentOrderDetail.receiverName }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">联系电话</label>
                            <p class="text-sm text-gray-900">{{ currentOrderDetail.receiverPhone }}</p>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-600">收货地址</label>
                            <p class="text-sm text-gray-900">{{ currentOrderDetail.shippingAddress }}</p>
                        </div>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="product-info mb-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">📦 商品信息</h3>
                    <el-table :data="currentOrderDetail.items || []" style="width: 100%" border>
                        <el-table-column prop="product_image_url" label="商品图片" width="100" align="center">
                            <template slot-scope="scope">
                                <img :src="scope.row.product_image_url || '/images/default-product.svg'"
                                     alt="商品图片"
                                     style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #e9ecef;"
                                     @error="scope.row.product_image_url = '/images/default-product.svg'">
                            </template>
                        </el-table-column>
                        <el-table-column prop="product_name" label="商品名称" min-width="180">
                            <template slot-scope="scope">
                                <div>
                                    <div class="font-medium text-gray-900">{{ scope.row.product_name || '未知商品' }}</div>
                                    <div class="text-sm text-gray-500 mt-1" v-if="scope.row.product_description">
                                        {{ scope.row.product_description }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="价格信息" width="140" align="center">
                            <template slot-scope="scope">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900">
                                        下单价: ¥{{ scope.row.product_price || '0.00' }}
                                    </div>
                                    <div class="text-xs text-green-600 mt-1" v-if="scope.row.current_price && scope.row.current_price !== scope.row.product_price">
                                        现价: ¥{{ scope.row.current_price }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="quantity" label="数量" width="80" align="center">
                            <template slot-scope="scope">
                                <el-tag size="small" type="info">{{ scope.row.quantity || 1 }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="小计" width="100" align="center">
                            <template slot-scope="scope">
                                <div class="font-medium text-red-600">
                                    ¥{{ scope.row.subtotal || (scope.row.product_price * scope.row.quantity).toFixed(2) }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="库存状态" width="100" align="center" v-if="currentOrderDetail.items && currentOrderDetail.items.some(item => item.product_stock !== undefined)">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.product_stock > 0 ? 'success' : 'danger'" size="small">
                                    {{ scope.row.product_stock > 0 ? `库存${scope.row.product_stock}` : '缺货' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 商品统计信息 -->
                    <div class="mt-4 p-3 bg-gray-50 rounded-lg" v-if="currentOrderDetail.items && currentOrderDetail.items.length > 0">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-600">
                                共 {{ currentOrderDetail.items.length }} 种商品，
                                总计 {{ currentOrderDetail.items.reduce((sum, item) => sum + (item.quantity || 1), 0) }} 件
                            </span>
                            <span class="font-medium text-lg text-red-600">
                                合计: ¥{{ currentOrderDetail.totalAmount || currentOrderDetail.total_amount || '0.00' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 订单备注 -->
                <div v-if="currentOrderDetail.remark" class="order-remark p-4 bg-yellow-50 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2 text-gray-800">💬 订单备注</h3>
                    <p class="text-sm text-gray-700">{{ currentOrderDetail.remark }}</p>
                </div>
            </div>
        </template>
        <template #footer>
            <div class="text-right">
                <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
                <el-button v-if="currentOrderDetail && currentOrderDetail.status === 2"
                           type="success" @click="shipOrder(currentOrderDetail)">
                    <i class="el-icon-truck"></i> 发货
                </el-button>
                <el-button v-if="currentOrderDetail && currentOrderDetail.status === 1"
                           type="warning" @click="cancelOrderAdmin(currentOrderDetail)">
                    <i class="el-icon-close"></i> 取消订单
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 系统设置对话框 -->
    <el-dialog :title="getSettingsTitle()" :visible.sync="settingsDialogVisible" width="600px">
        <template #default>
            <!-- 基本设置 -->
            <div v-if="currentSettingsType === 'basic'">
                <el-form :model="settingsForm" label-width="120px">
                    <el-form-item label="网站名称">
                        <el-input v-model="settingsForm.siteName" placeholder="请输入网站名称"></el-input>
                    </el-form-item>
                    <el-form-item label="网站描述">
                        <el-input v-model="settingsForm.siteDescription" type="textarea" placeholder="请输入网站描述"></el-input>
                    </el-form-item>
                    <el-form-item label="联系电话">
                        <el-input v-model="settingsForm.contactPhone" placeholder="请输入联系电话"></el-input>
                    </el-form-item>
                    <el-form-item label="联系邮箱">
                        <el-input v-model="settingsForm.contactEmail" placeholder="请输入联系邮箱"></el-input>
                    </el-form-item>
                    <el-form-item label="公司地址">
                        <el-input v-model="settingsForm.address" type="textarea" placeholder="请输入公司地址"></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 邮件设置 -->
            <div v-if="currentSettingsType === 'email'">
                <el-form :model="settingsForm" label-width="120px">
                    <el-form-item label="SMTP服务器">
                        <el-input v-model="settingsForm.smtpHost" placeholder="如：smtp.qq.com"></el-input>
                    </el-form-item>
                    <el-form-item label="SMTP端口">
                        <el-input-number v-model="settingsForm.smtpPort" :min="1" :max="65535"></el-input-number>
                    </el-form-item>
                    <el-form-item label="用户名">
                        <el-input v-model="settingsForm.smtpUser" placeholder="请输入邮箱用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="密码">
                        <el-input v-model="settingsForm.smtpPassword" type="password" placeholder="请输入邮箱密码"></el-input>
                    </el-form-item>
                    <el-form-item label="发件人邮箱">
                        <el-input v-model="settingsForm.fromEmail" placeholder="请输入发件人邮箱"></el-input>
                    </el-form-item>
                    <el-form-item label="发件人名称">
                        <el-input v-model="settingsForm.fromName" placeholder="请输入发件人名称"></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 安全设置 -->
            <div v-if="currentSettingsType === 'security'">
                <el-form :model="settingsForm" label-width="120px">
                    <el-form-item label="密码最小长度">
                        <el-input-number v-model="settingsForm.passwordMinLength" :min="6" :max="20"></el-input-number>
                    </el-form-item>
                    <el-form-item label="密码需要特殊字符">
                        <el-switch v-model="settingsForm.passwordRequireSpecial"></el-switch>
                    </el-form-item>
                    <el-form-item label="登录最大尝试次数">
                        <el-input-number v-model="settingsForm.loginMaxAttempts" :min="3" :max="10"></el-input-number>
                    </el-form-item>
                    <el-form-item label="会话超时时间">
                        <el-input-number v-model="settingsForm.sessionTimeout" :min="10" :max="120"></el-input-number>
                        <span class="ml-2 text-gray-500">分钟</span>
                    </el-form-item>
                    <el-form-item label="启用验证码">
                        <el-switch v-model="settingsForm.enableCaptcha"></el-switch>
                    </el-form-item>
                </el-form>
            </div>


        </template>
        <template #footer>
            <div class="text-right">
                <el-button @click="settingsDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveSettings">保存设置</el-button>
            </div>
        </template>
    </el-dialog>

</div>
<script>
    // Chart.js 多CDN加载策略
    function loadChartJS() {
        const cdnList = [
            'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
            'https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js',
            'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js',
            'https://cdn.bootcdn.net/ajax/libs/Chart.js/4.4.0/chart.umd.js'
        ];

        let currentIndex = 0;
        let currentScript = null;

        function tryLoadChart() {
            if (currentIndex >= cdnList.length) {
                console.error('所有Chart.js CDN都加载失败，使用简化版本');
                // 清理所有失败的脚本
                cleanupAllFailedScripts();
                // 创建一个简化的Chart对象，避免报错
                window.Chart = {
                    register: () => {},
                    Chart: function() {
                        return {
                            update: () => {},
                            destroy: () => {},
                            data: { datasets: [] }
                        };
                    }
                };
                return;
            }

            currentScript = document.createElement('script');
            currentScript.src = cdnList[currentIndex];
            currentScript.setAttribute('data-chart-attempt', currentIndex);

            currentScript.onload = () => {
                console.log('✅ Chart.js 加载成功:', cdnList[currentIndex]);
                // 清除所有之前失败的脚本标签
                cleanupFailedScripts();
            };

            currentScript.onerror = () => {
                console.warn('❌ Chart.js CDN加载失败:', cdnList[currentIndex]);
                // 立即移除失败的脚本
                if (currentScript && currentScript.parentNode) {
                    currentScript.parentNode.removeChild(currentScript);
                    console.log('🗑️ 已移除失败的脚本:', cdnList[currentIndex]);
                }
                currentIndex++;
                // 延迟一点再尝试下一个CDN，避免过快的请求
                setTimeout(tryLoadChart, 200);
            };

            document.head.appendChild(currentScript);
        }

        // 清理失败的脚本标签
        function cleanupFailedScripts() {
            const scripts = document.querySelectorAll('script[data-chart-attempt]');
            scripts.forEach(script => {
                // 只保留成功加载的脚本
                if (script !== currentScript) {
                    if (script.parentNode) {
                        script.parentNode.removeChild(script);
                        console.log('🧹 清理失败的Chart.js脚本:', script.src);
                    }
                }
            });
        }

        // 清理所有失败的脚本
        function cleanupAllFailedScripts() {
            const scripts = document.querySelectorAll('script[data-chart-attempt]');
            scripts.forEach(script => {
                if (script.parentNode) {
                    script.parentNode.removeChild(script);
                    console.log('🗑️ 清理所有Chart.js脚本:', script.src);
                }
            });
        }

        tryLoadChart();
    }

    // 立即开始加载Chart.js
    loadChartJS();
</script>
<script src="admin.js"></script>
</body>
</html>
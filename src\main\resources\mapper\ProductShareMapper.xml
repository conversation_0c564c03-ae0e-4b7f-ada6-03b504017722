<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.mapper.ProductShareMapper">

    <!-- 添加分享记录 -->
    <insert id="addShare" parameterType="cn.gzsf.javawebspringboot.entity.ProductShare">
        INSERT INTO product_share (product_id, user_phone, share_type, share_url, created_time)
        VALUES (#{productId}, #{userPhone}, #{shareType}, #{shareUrl}, #{createdTime})
    </insert>

    <!-- 获取产品分享总数 -->
    <select id="getProductShareCount" resultType="int">
        SELECT COUNT(*) FROM product_share 
        WHERE product_id = #{productId}
    </select>

    <!-- 获取用户分享列表 -->
    <select id="getUserShares" resultType="cn.gzsf.javawebspringboot.entity.ProductShare">
        SELECT
            ps.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl
        FROM product_share ps
        LEFT JOIN products p ON ps.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        WHERE ps.user_phone = #{userPhone}
        ORDER BY ps.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取用户分享总数 -->
    <select id="getUserShareCount" resultType="int">
        SELECT COUNT(*) FROM product_share 
        WHERE user_phone = #{userPhone}
    </select>

    <!-- 获取热门分享产品 -->
    <select id="getPopularShares" resultType="cn.gzsf.javawebspringboot.entity.ProductShare">
        SELECT
            ps.product_id,
            p.name as productName,
            p.image_url as productImageUrl,
            COUNT(*) as shareCount
        FROM product_share ps
        LEFT JOIN products p ON ps.product_id = p.id
        GROUP BY ps.product_id, p.name, p.image_url
        ORDER BY shareCount DESC
        LIMIT #{limit}
    </select>

    <!-- 获取分享统计（按类型） -->
    <select id="getShareStatsByType" resultType="map">
        SELECT 
            share_type as shareType,
            COUNT(*) as count
        FROM product_share
        GROUP BY share_type
        ORDER BY count DESC
    </select>

    <!-- 获取所有分享记录（管理员用） -->
    <select id="getAllShares" resultType="cn.gzsf.javawebspringboot.entity.ProductShare">
        SELECT
            ps.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            u.username as userName,
            ud.avatar_url as userAvatar,
            u.user_id as userId,
            u.phone as userPhone
        FROM product_share ps
        LEFT JOIN products p ON ps.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN user u ON ps.user_phone = u.phone COLLATE utf8mb4_unicode_ci
        LEFT JOIN user_detail ud ON u.phone = ud.phone COLLATE utf8mb4_unicode_ci
        ORDER BY ps.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取分享总数（管理员用） -->
    <select id="getTotalShareCount" resultType="int">
        SELECT COUNT(*) FROM product_share
    </select>

    <!-- 根据ID删除分享记录 -->
    <delete id="deleteShareById">
        DELETE FROM product_share WHERE id = #{id}
    </delete>

</mapper>

package cn.gzsf.javawebspringboot.service;

import java.util.Map;

/**
 * 管理员数据分析服务接口
 */
public interface AdminAnalysisService {
    
    /**
     * 获取数据分析数据
     * @return 分析数据
     */
    Map<String, Object> getAnalysisData();
    
    /**
     * 获取销售趋势数据
     * @param months 月份数
     * @return 销售趋势数据
     */
    Map<String, Object> getSalesTrend(int months);
    
    /**
     * 获取产品分类分布数据
     * @return 分类分布数据
     */
    Map<String, Object> getCategoryDistribution();
}

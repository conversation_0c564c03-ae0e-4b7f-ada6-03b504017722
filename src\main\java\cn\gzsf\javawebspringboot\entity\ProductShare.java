package cn.gzsf.javawebspringboot.entity;

import lombok.Data;

/**
 * 产品分享实体类
 */
@Data
public class ProductShare {
    private Long id;
    private Long productId;
    private String userPhone;
    private String shareType; // 分享类型：wechat, weibo, qq, link等
    private String shareUrl;
    private Long createdTime;
    
    // 关联查询字段
    private String productName;
    private String productImageUrl;
    private String userName;
}

package cn.gzsf.javawebspringboot.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 管理员数据分析Mapper接口
 */
@Mapper
public interface AdminAnalysisMapper {

    /**
     * 获取今日销售额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM user_order " +
            "WHERE status = 4 AND DATE(FROM_UNIXTIME(created_time/1000)) = CURDATE()")
    Double getTodaySales();

    /**
     * 获取本月销售额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM user_order " +
            "WHERE status = 4 AND YEAR(FROM_UNIXTIME(created_time/1000)) = YEAR(NOW()) " +
            "AND MONTH(FROM_UNIXTIME(created_time/1000)) = MONTH(NOW())")
    Double getMonthlySales();

    /**
     * 获取年度销售额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM user_order " +
            "WHERE status = 4 AND YEAR(FROM_UNIXTIME(created_time/1000)) = YEAR(NOW())")
    Double getYearlySales();

    /**
     * 获取今日订单数
     */
    @Select("SELECT COUNT(*) FROM user_order " +
            "WHERE DATE(FROM_UNIXTIME(created_time/1000)) = CURDATE()")
    int getTodayOrders();

    /**
     * 获取本月订单数
     */
    @Select("SELECT COUNT(*) FROM user_order " +
            "WHERE YEAR(FROM_UNIXTIME(created_time/1000)) = YEAR(NOW()) " +
            "AND MONTH(FROM_UNIXTIME(created_time/1000)) = MONTH(NOW())")
    int getMonthlyOrders();

    /**
     * 获取本月新增用户数
     */
    @Select("SELECT COUNT(*) FROM user " +
            "WHERE YEAR(FROM_UNIXTIME(register_time/1000)) = YEAR(NOW()) " +
            "AND MONTH(FROM_UNIXTIME(register_time/1000)) = MONTH(NOW())")
    int getNewUsersThisMonth();

    /**
     * 获取本月活跃用户数（有订单的用户）
     */
    @Select("SELECT COUNT(DISTINCT user_phone) FROM user_order " +
            "WHERE YEAR(FROM_UNIXTIME(created_time/1000)) = YEAR(NOW()) " +
            "AND MONTH(FROM_UNIXTIME(created_time/1000)) = MONTH(NOW())")
    int getActiveUsersThisMonth();

    /**
     * 获取指定月份的销售额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM user_order " +
            "WHERE status = 4 AND DATE_FORMAT(FROM_UNIXTIME(created_time/1000), '%Y-%m') = #{month}")
    Double getSalesByMonth(@Param("month") String month);

    /**
     * 获取产品分类分布
     */
    @Select("SELECT c.name as category_name, COUNT(p.id) as product_count " +
            "FROM categories c " +
            "LEFT JOIN product_category pc ON c.id = pc.category_id " +
            "LEFT JOIN products p ON pc.product_id = p.id " +
            "WHERE c.is_active = 1 " +
            "GROUP BY c.id, c.name " +
            "ORDER BY product_count DESC")
    List<Map<String, Object>> getCategoryDistribution();
}

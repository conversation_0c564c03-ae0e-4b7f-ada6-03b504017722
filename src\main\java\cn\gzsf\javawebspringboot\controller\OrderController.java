package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.dto.OrderCreateDTO;
import cn.gzsf.javawebspringboot.entity.UserOrder;
import cn.gzsf.javawebspringboot.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/api/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public Map<String, Object> createOrder(@RequestBody OrderCreateDTO orderCreateDTO) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = orderService.createOrder(orderCreateDTO);
            if (orderNo != null) {
                result.put("success", true);
                result.put("message", "订单创建成功");
                result.put("orderNo", orderNo);
            } else {
                result.put("success", false);
                result.put("message", "订单创建失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list/{userIdentifier}")
    public Map<String, Object> getUserOrders(@PathVariable String userIdentifier) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询用户订单列表
            String sql = "SELECT uo.*, " +
                        "GROUP_CONCAT(CONCAT(od.product_name, '×', od.quantity) SEPARATOR ', ') as product_summary " +
                        "FROM user_order uo " +
                        "LEFT JOIN order_detail od ON uo.id = od.order_id " +
                        "WHERE uo.user_phone = ? " +
                        "GROUP BY uo.id " +
                        "ORDER BY uo.created_time DESC";

            List<Map<String, Object>> orders = jdbcTemplate.queryForList(sql, userIdentifier);

            result.put("success", true);
            result.put("data", orders);
            result.put("count", orders.size());
            result.put("message", "获取订单列表成功");

            System.out.println("📋 获取用户订单列表成功: " + userIdentifier + ", 共 " + orders.size() + " 个订单");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderNo}")
    public Map<String, Object> getOrderDetail(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询订单基本信息
            String orderSql = "SELECT * FROM user_order WHERE order_no = ?";
            List<Map<String, Object>> orders = jdbcTemplate.queryForList(orderSql, orderNo);

            if (orders.isEmpty()) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            Map<String, Object> order = orders.get(0);

            // 查询订单商品详情
            String detailSql = "SELECT * FROM order_detail WHERE order_id = ?";
            List<Map<String, Object>> orderDetails = jdbcTemplate.queryForList(detailSql, order.get("id"));

            order.put("orderDetails", orderDetails);

            result.put("success", true);
            result.put("data", order);
            result.put("message", "获取订单详情成功");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单详情失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderNo}")
    public Map<String, Object> getOrderDetail(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            UserOrder order = orderService.getOrderByOrderNo(orderNo);
            if (order != null) {
                result.put("success", true);
                result.put("data", order);
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情（前端用）
     */
    @GetMapping("/{orderNo}")
    public Map<String, Object> getOrderDetailForFrontend(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderSql = "SELECT * FROM user_order WHERE order_no = ?";
            List<Map<String, Object>> orders = jdbcTemplate.queryForList(orderSql, orderNo);

            if (!orders.isEmpty()) {
                Map<String, Object> order = orders.get(0);

                // 获取订单商品明细（包含商品详细信息）
                String itemSql = "SELECT od.*, p.name as product_name, p.description as product_description, " +
                                "COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as product_image_url, " +
                                "p.price as current_price " +
                                "FROM order_detail od " +
                                "LEFT JOIN products p ON od.product_id = p.id " +
                                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                                "WHERE od.order_id = ?";
                List<Map<String, Object>> items = jdbcTemplate.queryForList(itemSql, order.get("id"));
                order.put("items", items);

                result.put("success", true);
                result.put("data", order);
                result.put("message", "获取订单详情成功");
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 支付订单
     */
    @PostMapping("/pay/{orderNo}")
    public Map<String, Object> payOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.payOrder(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "支付成功");
            } else {
                result.put("success", false);
                result.put("message", "支付失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderNo}")
    public Map<String, Object> cancelOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.cancelOrder(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "订单已取消");
            } else {
                result.put("success", false);
                result.put("message", "取消订单失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 确认收货
     */
    @PostMapping("/confirm/{orderNo}")
    public Map<String, Object> confirmReceived(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.confirmReceived(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "确认收货成功");
            } else {
                result.put("success", false);
                result.put("message", "确认收货失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{orderNo}")
    public Map<String, Object> deleteOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.deleteOrder(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "订单已删除");
            } else {
                result.put("success", false);
                result.put("message", "删除订单失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }



    /**
     * 更新订单状态
     */
    @PostMapping("/{orderNo}/status")
    public Map<String, Object> updateOrderStatus(@PathVariable String orderNo, @RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer status = Integer.valueOf(request.get("status").toString());
            String updateSql = "UPDATE user_order SET status = ?, updated_time = ? WHERE order_no = ?";
            long currentTime = System.currentTimeMillis();
            int updated = jdbcTemplate.update(updateSql, status, currentTime, orderNo);

            if (updated > 0) {
                result.put("success", true);
                result.put("message", "订单状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "订单不存在或更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新订单状态失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list/{userPhone}")
    public Map<String, Object> getOrderList(@PathVariable String userPhone,
                                          @RequestParam(defaultValue = "1") int page,
                                          @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;

            // 获取订单列表
            String orderSql = "SELECT * FROM user_order WHERE user_phone = ? ORDER BY created_time DESC LIMIT ? OFFSET ?";
            List<Map<String, Object>> orders = jdbcTemplate.queryForList(orderSql, userPhone, size, offset);

            // 为每个订单获取商品明细（包含商品详细信息）
            for (Map<String, Object> order : orders) {
                Long orderId = Long.valueOf(order.get("id").toString());
                String itemSql = "SELECT od.*, p.name as product_name, p.description as product_description, " +
                                "COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as product_image_url, " +
                                "p.price as current_price " +
                                "FROM order_detail od " +
                                "LEFT JOIN products p ON od.product_id = p.id " +
                                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                                "WHERE od.order_id = ?";
                List<Map<String, Object>> items = jdbcTemplate.queryForList(itemSql, orderId);
                order.put("items", items);
            }

            // 获取总数
            String countSql = "SELECT COUNT(*) FROM user_order WHERE user_phone = ?";
            Integer total = jdbcTemplate.queryForObject(countSql, Integer.class, userPhone);

            result.put("success", true);
            result.put("data", orders);

            // 创建分页信息Map（JDK 1.8兼容）
            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", page);
            pagination.put("pageSize", size);
            pagination.put("total", total);
            pagination.put("totalPages", (int) Math.ceil((double) total / size));
            result.put("pagination", pagination);

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 创建订单（增强版本，支持前端购物车结算）
     */
    @PostMapping("/create-enhanced")
    public Map<String, Object> createOrderEnhanced(@RequestBody Map<String, Object> orderData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String userPhone = (String) orderData.get("userPhone");
            Long addressId = Long.valueOf(orderData.get("addressId").toString());
            String remark = (String) orderData.get("remark");
            Double subtotal = Double.valueOf(orderData.get("subtotal").toString());
            Double shipping = Double.valueOf(orderData.get("shipping").toString());
            Double total = Double.valueOf(orderData.get("total").toString());

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> items = (List<Map<String, Object>>) orderData.get("items");

            // 生成订单号
            String orderNo = generateOrderNo();

            // 获取收货地址信息
            String addressSql = "SELECT * FROM addresses WHERE id = ?";
            List<Map<String, Object>> addresses = jdbcTemplate.queryForList(addressSql, addressId);
            if (addresses.isEmpty()) {
                result.put("success", false);
                result.put("message", "收货地址不存在");
                return result;
            }

            Map<String, Object> address = addresses.get(0);
            String receiverName = (String) address.get("receiver_name");
            String receiverPhone = (String) address.get("receiver_phone");
            String fullAddress = address.get("province") + " " + address.get("city") + " " +
                               address.get("district") + " " + address.get("detail_address");

            // 插入订单主表
            String insertOrderSql = "INSERT INTO user_order (order_no, user_phone, receiver_name, receiver_phone, " +
                                   "receiver_address, total_amount, remark, status, " +
                                   "created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, ?)";

            long currentTime = System.currentTimeMillis();
            jdbcTemplate.update(insertOrderSql, orderNo, userPhone, receiverName, receiverPhone,
                              fullAddress, total, remark, currentTime, currentTime);

            // 获取订单ID
            String getOrderIdSql = "SELECT id FROM user_order WHERE order_no = ?";
            Long orderId = jdbcTemplate.queryForObject(getOrderIdSql, Long.class, orderNo);

            // 插入订单商品明细
            String insertItemSql = "INSERT INTO order_detail (order_id, product_id, product_name, product_price, quantity, subtotal) " +
                                  "VALUES (?, ?, ?, ?, ?, ?)";

            for (Map<String, Object> item : items) {
                Long productId = item.get("id") != null ? Long.valueOf(item.get("id").toString()) : 0L;
                String productName = (String) item.get("name");
                Double price = Double.valueOf(item.get("price").toString());
                Integer quantity = Integer.valueOf(item.get("quantity").toString());
                Double itemSubtotal = price * quantity;

                jdbcTemplate.update(insertItemSql, orderId, productId, productName, price, quantity, itemSubtotal);
            }

            result.put("success", true);
            result.put("message", "订单创建成功");
            result.put("orderNo", orderNo);
            result.put("orderId", orderId);

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "创建订单失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取所有订单（管理员用）
     */
    @GetMapping("/admin/all")
    public Map<String, Object> getAllOrders(@RequestParam(defaultValue = "1") int page,
                                          @RequestParam(defaultValue = "10") int size,
                                          @RequestParam(required = false) Integer status) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;

            String whereSql = status != null ? " WHERE status = ?" : "";
            String orderSql = "SELECT * FROM user_order" + whereSql + " ORDER BY created_time DESC LIMIT ? OFFSET ?";

            List<Map<String, Object>> orders;
            Integer total;

            if (status != null) {
                orders = jdbcTemplate.queryForList(orderSql, status, size, offset);
                total = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM user_order WHERE status = ?", Integer.class, status);
            } else {
                orders = jdbcTemplate.queryForList(orderSql, size, offset);
                total = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM user_order", Integer.class);
            }

            // 为每个订单获取商品明细
            for (Map<String, Object> order : orders) {
                Long orderId = Long.valueOf(order.get("id").toString());
                String itemSql = "SELECT * FROM order_detail WHERE order_id = ?";
                List<Map<String, Object>> items = jdbcTemplate.queryForList(itemSql, orderId);
                order.put("items", items);
            }

            result.put("success", true);
            result.put("data", orders);

            // 创建分页信息Map（JDK 1.8兼容）
            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", page);
            pagination.put("pageSize", size);
            pagination.put("total", total);
            pagination.put("totalPages", (int) Math.ceil((double) total / size));
            result.put("pagination", pagination);

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        return "ORD" + timestamp + uuid;
    }
}

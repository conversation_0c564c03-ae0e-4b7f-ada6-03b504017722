/**
 * JWT认证管理器
 * 处理用户登录、注册、令牌管理等功能
 */
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('authToken');
        this.user = JSON.parse(localStorage.getItem('currentUser') || 'null');
        this.setupAxiosInterceptors();
        this.checkTokenExpiry();
    }

    /**
     * 设置Axios拦截器，自动添加JWT令牌
     */
    setupAxiosInterceptors() {
        // 请求拦截器 - 自动添加JWT令牌
        axios.interceptors.request.use(
            (config) => {
                if (this.token) {
                    config.headers['Authorization'] = `Bearer ${this.token}`;
                    config.headers['X-Auth-Token'] = this.token;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // 响应拦截器 - 处理认证错误
        axios.interceptors.response.use(
            (response) => {
                return response;
            },
            (error) => {
                if (error.response && error.response.status === 401) {
                    console.log('🔒 认证失败，清除本地认证信息');
                    this.logout();
                    this.showLoginRequired();
                }
                return Promise.reject(error);
            }
        );
    }

    /**
     * 用户登录
     */
    async login(credentials) {
        try {
            const response = await axios.post('/api/user/login', credentials);
            
            if (response.data.success) {
                this.token = response.data.token;
                this.user = response.data.user;
                
                // 保存到本地存储
                localStorage.setItem('authToken', this.token);
                localStorage.setItem('currentUser', JSON.stringify(this.user));
                
                console.log('✅ 登录成功:', this.user.username);
                
                // 触发登录成功事件
                this.dispatchAuthEvent('login', { user: this.user });
                
                return {
                    success: true,
                    user: this.user,
                    isAdmin: response.data.isAdmin,
                    redirectUrl: response.data.redirectUrl
                };
            } else {
                return {
                    success: false,
                    message: response.data.message
                };
            }
        } catch (error) {
            console.error('❌ 登录失败:', error);
            return {
                success: false,
                message: error.response?.data?.message || '登录失败，请重试'
            };
        }
    }

    /**
     * 用户注册
     */
    async register(userData) {
        try {
            const response = await axios.post('/api/user/register', userData);
            
            if (response.data.success) {
                console.log('✅ 注册成功');
                return {
                    success: true,
                    message: response.data.message
                };
            } else {
                return {
                    success: false,
                    message: response.data.message
                };
            }
        } catch (error) {
            console.error('❌ 注册失败:', error);
            return {
                success: false,
                message: error.response?.data?.message || '注册失败，请重试'
            };
        }
    }

    /**
     * 用户登出
     */
    logout() {
        this.token = null;
        this.user = null;
        
        // 清除本地存储
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        
        console.log('🔓 用户已登出');
        
        // 触发登出事件
        this.dispatchAuthEvent('logout');
    }

    /**
     * 检查用户是否已登录
     */
    isAuthenticated() {
        return this.token !== null && this.user !== null;
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        return this.user;
    }

    /**
     * 获取用户资料
     */
    async getUserProfile() {
        try {
            const response = await axios.get('/api/user/profile');
            
            if (response.data.success) {
                this.user = response.data.user;
                localStorage.setItem('currentUser', JSON.stringify(this.user));
                return {
                    success: true,
                    user: this.user
                };
            } else {
                return {
                    success: false,
                    message: response.data.message
                };
            }
        } catch (error) {
            console.error('❌ 获取用户资料失败:', error);
            return {
                success: false,
                message: '获取用户资料失败'
            };
        }
    }

    /**
     * 修改密码
     */
    async changePassword(passwordData) {
        try {
            const response = await axios.post('/api/user/change-password', passwordData);
            
            if (response.data.success) {
                console.log('✅ 密码修改成功');
                return {
                    success: true,
                    message: response.data.message
                };
            } else {
                return {
                    success: false,
                    message: response.data.message
                };
            }
        } catch (error) {
            console.error('❌ 密码修改失败:', error);
            return {
                success: false,
                message: error.response?.data?.message || '密码修改失败'
            };
        }
    }

    /**
     * 检查令牌是否即将过期
     */
    checkTokenExpiry() {
        if (!this.token) return;

        try {
            // 解析JWT令牌（简单解析，不验证签名）
            const payload = JSON.parse(atob(this.token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            const timeUntilExpiry = payload.exp - currentTime;

            // 如果令牌在30分钟内过期，显示提醒
            if (timeUntilExpiry > 0 && timeUntilExpiry < 1800) {
                console.log('⚠️ 令牌即将过期，剩余时间:', timeUntilExpiry, '秒');
                this.showTokenExpiryWarning(timeUntilExpiry);
            } else if (timeUntilExpiry <= 0) {
                console.log('🔒 令牌已过期');
                this.logout();
                this.showLoginRequired();
            }
        } catch (error) {
            console.error('❌ 解析令牌失败:', error);
        }
    }

    /**
     * 显示令牌过期警告
     */
    showTokenExpiryWarning(timeLeft) {
        const minutes = Math.floor(timeLeft / 60);
        const message = `您的登录状态将在 ${minutes} 分钟后过期，请及时保存您的操作。`;
        
        // 这里可以显示一个提示框
        if (window.ElementUI && window.ElementUI.Message) {
            window.ElementUI.Message.warning(message);
        } else {
            console.warn(message);
        }
    }

    /**
     * 显示需要登录的提示
     */
    showLoginRequired() {
        const message = '您的登录状态已过期，请重新登录。';
        
        if (window.ElementUI && window.ElementUI.Message) {
            window.ElementUI.Message.error(message);
        } else {
            alert(message);
        }

        // 3秒后跳转到登录页面
        setTimeout(() => {
            if (window.location.pathname !== '/land.html') {
                window.location.href = '/land.html';
            }
        }, 3000);
    }

    /**
     * 触发认证相关事件
     */
    dispatchAuthEvent(type, data = {}) {
        const event = new CustomEvent(`auth:${type}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * 检查手机号是否已注册
     */
    async checkPhone(phone) {
        try {
            const response = await axios.get(`/api/user/check-phone?phone=${phone}`);
            return response.data;
        } catch (error) {
            console.error('❌ 检查手机号失败:', error);
            return { success: false, message: '检查失败' };
        }
    }

    /**
     * 检查用户ID是否已存在
     */
    async checkUserId(userId) {
        try {
            const response = await axios.get(`/api/user/check-userid?userId=${userId}`);
            return response.data;
        } catch (error) {
            console.error('❌ 检查用户ID失败:', error);
            return { success: false, message: '检查失败' };
        }
    }
}

// 创建全局认证管理器实例
window.authManager = new AuthManager();

// 监听认证事件
document.addEventListener('auth:login', (event) => {
    console.log('🔑 用户登录事件:', event.detail);
    // 可以在这里更新UI，显示用户信息等
});

document.addEventListener('auth:logout', (event) => {
    console.log('🔓 用户登出事件:', event.detail);
    // 可以在这里清理UI，隐藏用户相关内容等
});

console.log('🔐 JWT认证管理器已初始化');

package cn.gzsf.javawebspringboot.config;

import cn.gzsf.javawebspringboot.interceptor.JwtAuthenticationInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.*;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtAuthenticationInterceptor jwtAuthenticationInterceptor;

    @Value("${file.upload.dir}")
    private String uploadDir;

    /**
     * 添加JWT认证拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtAuthenticationInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                    // 静态资源
                    "/css/**",
                    "/js/**",
                    "/images/**",
                    "/uploads/**",
                    "/favicon.ico",

                    // HTML页面
                    "/",
                    "/index.html",
                    "/land.html",
                    "/admin.html",

                    // 公开API
                    "/api/user/login",
                    "/api/user/register",
                    "/api/user/check-phone",
                    "/api/user/check-userid",
                    "/api/products/all",
                    "/api/products/new",
                    "/api/products/category/**",
                    "/admin/category/frontend",
                    "/api/carousel/images",

                    // 错误页面
                    "/error"
                );

        System.out.println("✅ JWT认证拦截器已注册");
    }

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*") // 允许所有域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("Authorization", "X-Auth-Token")
                .allowCredentials(true)
                .maxAge(3600);

        System.out.println("✅ CORS跨域配置已设置");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取当前工作目录
        String currentDir = System.getProperty("user.dir");
        String bannerPath = "file:" + currentDir + "/src/main/resources/static/images/banner/";
        String imagesPath = "file:" + currentDir + "/src/main/resources/static/images/";

        // 配置轮播图访问路径 - 映射到banner文件夹（使用绝对路径）
        registry.addResourceHandler("/images/banner/**")
                .addResourceLocations(bannerPath)
                .addResourceLocations("classpath:/static/images/banner/");

        // 配置普通图片访问路径
        registry.addResourceHandler("/images/**")
                .addResourceLocations(imagesPath)
                .addResourceLocations("classpath:/static/images/");

        // 配置默认静态资源路径
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");

        System.out.println("🔧 静态资源映射配置:");
        System.out.println("当前工作目录: " + currentDir);
        System.out.println("轮播图路径: /images/banner/** -> " + bannerPath);
        System.out.println("普通图片路径: /images/** -> " + imagesPath);
        System.out.println("静态资源路径: /** -> classpath:/static/");

        // 验证banner文件夹是否存在
        try {
            java.io.File bannerDir = new java.io.File(currentDir + "/src/main/resources/static/images/banner");
            System.out.println("Banner目录存在: " + bannerDir.exists());
            System.out.println("Banner目录绝对路径: " + bannerDir.getAbsolutePath());

            // 如果目录不存在，创建它
            if (!bannerDir.exists()) {
                boolean created = bannerDir.mkdirs();
                System.out.println("创建Banner目录: " + (created ? "成功" : "失败"));
            }

            if (bannerDir.exists()) {
                java.io.File[] files = bannerDir.listFiles();
                System.out.println("Banner目录中文件数量: " + (files != null ? files.length : 0));

                // 显示前5个文件名
                if (files != null && files.length > 0) {
                    System.out.println("Banner文件:");
                    for (int i = 0; i < Math.min(5, files.length); i++) {
                        System.out.println("  " + files[i].getName());
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ 检查Banner目录失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
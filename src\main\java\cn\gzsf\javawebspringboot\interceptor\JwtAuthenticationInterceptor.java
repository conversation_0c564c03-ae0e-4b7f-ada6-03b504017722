package cn.gzsf.javawebspringboot.interceptor;

import cn.gzsf.javawebspringboot.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证拦截器
 * 拦截需要认证的请求，验证JWT令牌
 */
@Component
public class JwtAuthenticationInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 跨域预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取请求路径
        String requestURI = request.getRequestURI();
        System.out.println("🔍 JWT拦截器检查请求: " + requestURI);

        // 不需要认证的路径
        if (isPublicPath(requestURI)) {
            System.out.println("✅ 公开路径，无需认证: " + requestURI);
            return true;
        }

        // 从请求头获取token
        String token = extractToken(request);
        
        if (token == null) {
            System.out.println("❌ 未找到认证令牌");
            sendUnauthorizedResponse(response, "未找到认证令牌，请先登录");
            return false;
        }

        try {
            // 验证token
            if (!jwtUtil.validateToken(token)) {
                System.out.println("❌ 令牌验证失败");
                sendUnauthorizedResponse(response, "令牌无效或已过期，请重新登录");
                return false;
            }

            // 提取用户信息并设置到请求属性中
            String userId = jwtUtil.extractUserId(token);
            String phone = jwtUtil.extractPhone(token);
            String username = jwtUtil.extractUsername(token);
            
            request.setAttribute("currentUserId", userId);
            request.setAttribute("currentUserPhone", phone);
            request.setAttribute("currentUsername", username);
            
            System.out.println("✅ 用户认证成功: " + username + " (" + userId + ")");
            return true;

        } catch (Exception e) {
            System.out.println("❌ 令牌验证异常: " + e.getMessage());
            sendUnauthorizedResponse(response, e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为公开路径（不需要认证）
     */
    private boolean isPublicPath(String path) {
        // 公开的API路径
        String[] publicPaths = {
            "/api/user/login",           // 用户登录
            "/api/user/register",        // 用户注册
            "/api/user/check-phone",     // 检查手机号
            "/api/user/check-userid",    // 检查用户ID
            "/api/products/all",         // 获取所有产品
            "/api/products/new",         // 获取新产品
            "/api/products/category/",   // 按分类获取产品
            "/admin/category/frontend",  // 前端分类数据
            "/api/carousel/images",      // 轮播图
            "/uploads/",                 // 静态资源
            "/images/",                  // 图片资源
            "/css/",                     // CSS文件
            "/js/",                      // JS文件
            "/",                         // 根路径
            "/index.html",               // 主页
            "/land.html",                // 登录页
            "/admin.html"                // 管理页面（页面本身不需要认证，但API需要）
        };

        for (String publicPath : publicPaths) {
            if (path.startsWith(publicPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从请求中提取JWT令牌
     */
    private String extractToken(HttpServletRequest request) {
        // 1. 从Authorization头获取 (Bearer token格式)
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 2. 从自定义头获取
        String tokenHeader = request.getHeader("X-Auth-Token");
        if (tokenHeader != null && !tokenHeader.trim().isEmpty()) {
            return tokenHeader;
        }

        // 3. 从请求参数获取
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.trim().isEmpty()) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 发送未授权响应
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Auth-Token");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", 401);
        result.put("message", message);
        result.put("timestamp", System.currentTimeMillis());

        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
    }
}

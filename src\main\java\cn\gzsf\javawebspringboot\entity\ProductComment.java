package cn.gzsf.javawebspringboot.entity;

import lombok.Data;

/**
 * 产品评论实体类
 */
@Data
public class ProductComment {
    private Long id;
    private Long productId;
    private String userPhone;
    private String userName;
    private String userAvatar;
    private String content;
    private Integer rating; // 评分 1-5星
    private String images; // 评论图片，多个图片用逗号分隔
    private Long createdTime;
    private Long updatedTime;
    private Integer status; // 状态：0-待审核，1-已通过，2-已拒绝
    
    // 关联查询字段
    private String productName;
    private String productImageUrl;
}

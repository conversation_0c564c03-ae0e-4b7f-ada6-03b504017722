package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.User;
import cn.gzsf.javawebspringboot.service.UserService;
import cn.gzsf.javawebspringboot.util.JwtUtil;
import cn.gzsf.javawebspringboot.util.PasswordEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

// 控制器类，处理用户登录、注册请求
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // 处理用户登录请求 - 支持JWT和密码加密
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 输入验证
            if (user == null ||
                (user.getUserId() == null && user.getPhone() == null) ||
                user.getPassword() == null || user.getPassword().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名/手机号和密码不能为空");
                return result;
            }

            String loginIdentifier = user.getUserId() != null ? user.getUserId() : user.getPhone();

            // 防止SQL注入 - 基本验证
            if (loginIdentifier.length() > 50 || user.getPassword().length() > 100) {
                result.put("success", false);
                result.put("message", "输入参数长度超出限制");
                return result;
            }

            // 查询用户信息
            User dbUser = userService.findUserByIdentifier(loginIdentifier);
            if (dbUser == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 验证密码
            boolean passwordMatches = false;

            // 检查是否为管理员账户的特殊处理
            if (("admin".equals(dbUser.getUserId()) || "15120248009".equals(dbUser.getPhone()))
                && "200117Wc$".equals(user.getPassword()) && "200117Wc$".equals(dbUser.getPassword())) {
                passwordMatches = true;
            } else {
                // 尝试BCrypt验证（新密码）
                try {
                    passwordMatches = passwordEncoder.matches(user.getPassword(), dbUser.getPassword());
                } catch (Exception e) {
                    // 如果BCrypt验证失败，尝试明文比较（兼容旧密码）
                    passwordMatches = user.getPassword().equals(dbUser.getPassword());
                }
            }

            if (!passwordMatches) {
                result.put("success", false);
                result.put("message", "密码错误");
                return result;
            }

            // 生成JWT令牌
            String token = jwtUtil.generateToken(
                dbUser.getUserId(),
                dbUser.getPhone(),
                dbUser.getUsername()
            );

            // 构建用户信息（不包含密码等敏感信息）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", dbUser.getId());
            userInfo.put("userId", dbUser.getUserId());
            userInfo.put("username", dbUser.getUsername());
            userInfo.put("phone", dbUser.getPhone());
            userInfo.put("avatar", dbUser.getAvatar());
            userInfo.put("signature", dbUser.getSignature());

            result.put("success", true);
            result.put("message", "登录成功");
            result.put("token", token);
            result.put("user", userInfo);

            // 检查是否为管理员
            if ("admin".equals(dbUser.getUserId()) || "15120248009".equals(dbUser.getPhone())) {
                result.put("redirectUrl", "admin.html");
                result.put("isAdmin", true);
                System.out.println("🔑 管理员登录成功: " + dbUser.getUsername());
            } else {
                result.put("isAdmin", false);
                System.out.println("🔑 用户登录成功: " + dbUser.getUsername());
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "登录失败: " + e.getMessage());
        }

        return result;
    }

    // 处理用户注册请求 - 支持密码加密
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody User user) {
        System.out.println("🔐 接收到注册请求: " + user.getUsername());
        Map<String, Object> result = new HashMap<>();

        try {
            // 输入验证
            if (user == null || user.getPhone() == null || user.getPassword() == null ||
                user.getUsername() == null || user.getUserId() == null) {
                result.put("success", false);
                result.put("message", "必填信息不能为空");
                return result;
            }

            // 密码格式验证
            PasswordEncoder.ValidationResult passwordValidation = passwordEncoder.validatePasswordFormat(user.getPassword());
            if (!passwordValidation.isValid()) {
                result.put("success", false);
                result.put("message", passwordValidation.getMessage());
                return result;
            }

            // 检查用户是否已存在
            if (userService.findUserByIdentifier(user.getPhone()) != null) {
                result.put("success", false);
                result.put("message", "该手机号已注册");
                return result;
            }

            if (userService.findUserByIdentifier(user.getUserId()) != null) {
                result.put("success", false);
                result.put("message", "该用户ID已存在");
                return result;
            }

            // 加密密码
            String encodedPassword = passwordEncoder.encode(user.getPassword());
            user.setPassword(encodedPassword);

            // 设置注册时间
            user.setRegisterTime(System.currentTimeMillis());

            // 注册用户
            boolean isRegistered = userService.register(user);
            if (isRegistered) {
                result.put("success", true);
                result.put("message", "注册成功");
                System.out.println("✅ 用户注册成功: " + user.getUsername());
            } else {
                result.put("success", false);
                result.put("message", "注册失败，请稍后重试");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "注册失败: " + e.getMessage());
        }

        return result;
    }

    // 检查手机号是否已注册
    @GetMapping("/check-phone")
    public Map<String, Object> checkPhone(@RequestParam String phone) {
        Map<String, Object> result = new HashMap<>();
        try {
            User existingUser = userService.findUserByIdentifier(phone);
            result.put("success", true);
            result.put("exists", existingUser != null);
            result.put("message", existingUser != null ? "手机号已注册" : "手机号可用");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        return result;
    }

    // 检查用户ID是否已存在
    @GetMapping("/check-userid")
    public Map<String, Object> checkUserId(@RequestParam String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            User existingUser = userService.findUserByIdentifier(userId);
            result.put("success", true);
            result.put("exists", existingUser != null);
            result.put("message", existingUser != null ? "用户ID已存在" : "用户ID可用");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        return result;
    }

    // 获取当前用户信息
    @GetMapping("/profile")
    public Map<String, Object> getUserProfile(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String currentUserId = (String) request.getAttribute("currentUserId");
            if (currentUserId == null) {
                result.put("success", false);
                result.put("message", "用户未登录");
                return result;
            }

            User user = userService.findUserByIdentifier(currentUserId);
            if (user != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("userId", user.getUserId());
                userInfo.put("username", user.getUsername());
                userInfo.put("phone", user.getPhone());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("signature", user.getSignature());
                userInfo.put("registerTime", user.getRegisterTime());

                result.put("success", true);
                result.put("user", userInfo);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取用户信息失败: " + e.getMessage());
        }
        return result;
    }

    // 修改密码
    @PostMapping("/change-password")
    public Map<String, Object> changePassword(@RequestBody Map<String, String> passwordData, HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String currentUserId = (String) request.getAttribute("currentUserId");
            if (currentUserId == null) {
                result.put("success", false);
                result.put("message", "用户未登录");
                return result;
            }

            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            if (oldPassword == null || newPassword == null) {
                result.put("success", false);
                result.put("message", "旧密码和新密码不能为空");
                return result;
            }

            // 验证新密码格式
            PasswordEncoder.ValidationResult validation = passwordEncoder.validatePasswordFormat(newPassword);
            if (!validation.isValid()) {
                result.put("success", false);
                result.put("message", validation.getMessage());
                return result;
            }

            // 获取用户信息
            User user = userService.findUserByIdentifier(currentUserId);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 验证旧密码
            boolean oldPasswordMatches = false;
            try {
                oldPasswordMatches = passwordEncoder.matches(oldPassword, user.getPassword());
            } catch (Exception e) {
                // 兼容明文密码
                oldPasswordMatches = oldPassword.equals(user.getPassword());
            }

            if (!oldPasswordMatches) {
                result.put("success", false);
                result.put("message", "旧密码错误");
                return result;
            }

            // 加密新密码
            String encodedNewPassword = passwordEncoder.encode(newPassword);

            // 更新密码
            boolean updated = userService.updatePassword(currentUserId, encodedNewPassword);
            if (updated) {
                result.put("success", true);
                result.put("message", "密码修改成功");
                System.out.println("🔐 用户密码修改成功: " + currentUserId);
            } else {
                result.put("success", false);
                result.put("message", "密码修改失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "密码修改失败: " + e.getMessage());
        }
        return result;
    }

    // 控制台查询用户列表接口
    @GetMapping("/userList")
    public List<User> getUserList() {
        List<User> userList = userService.getAllUsers();
        userList.forEach(user -> {
            System.out.println("用户ID：" + user.getUserId()); // 确保输出非 null
        });
        return userList;
    }

    // 分页获取用户列表（支持搜索）
    @GetMapping("/userList/page")
    public Map<String, Object> getUsersPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder();
            List<Object> params = new ArrayList<>();

            if (keyword != null && !keyword.trim().isEmpty()) {
                whereClause.append(" WHERE (username LIKE ? OR phone LIKE ? OR user_id LIKE ?)");
                String searchKeyword = "%" + keyword.trim() + "%";
                params.add(searchKeyword);
                params.add(searchKeyword);
                params.add(searchKeyword);
            }

            if (status != null && !status.trim().isEmpty()) {
                if (whereClause.length() > 0) {
                    whereClause.append(" AND status = ?");
                } else {
                    whereClause.append(" WHERE status = ?");
                }
                params.add(status.trim());
            }

            if (startDate != null && !startDate.trim().isEmpty() &&
                endDate != null && !endDate.trim().isEmpty()) {
                try {
                    // 将日期字符串转换为时间戳
                    long startTime = java.sql.Date.valueOf(startDate).getTime();
                    long endTime = java.sql.Date.valueOf(endDate).getTime() + 24 * 60 * 60 * 1000 - 1; // 结束日期的23:59:59

                    if (whereClause.length() > 0) {
                        whereClause.append(" AND register_time BETWEEN ? AND ?");
                    } else {
                        whereClause.append(" WHERE register_time BETWEEN ? AND ?");
                    }
                    params.add(startTime);
                    params.add(endTime);
                } catch (Exception e) {
                    System.err.println("日期解析失败: " + e.getMessage());
                }
            }

            // 查询用户数据
            String sql = "SELECT * FROM user" + whereClause.toString() + " ORDER BY register_time DESC LIMIT ?, ?";
            params.add(offset);
            params.add(size);

            List<User> users = jdbcTemplate.query(sql, params.toArray(), (rs, rowNum) -> {
                User user = new User();
                user.setUserId(rs.getString("user_id"));
                user.setUsername(rs.getString("username"));
                user.setPhone(rs.getString("phone"));
                user.setPassword(rs.getString("password"));
                user.setRegisterTime(rs.getLong("register_time"));
                return user;
            });

            // 获取总数（应用相同的搜索条件）
            String countSql = "SELECT COUNT(*) FROM user" + whereClause.toString();
            List<Object> countParams = new ArrayList<>(params.subList(0, params.size() - 2)); // 移除LIMIT参数
            int total = jdbcTemplate.queryForObject(countSql, Integer.class, countParams.toArray());

            result.put("data", users);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            result.put("success", true);

            System.out.println("🔍 用户搜索: 关键词=" + keyword + ", 状态=" + status + ", 找到 " + users.size() + " 个用户");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取用户列表失败: " + e.getMessage());

            // 如果出错，回退到原来的方式
            try {
                int offset = (page - 1) * size;
                List<User> users = userService.getUsersPage(offset, size);
                int total = userService.getTotalUserCount();
                result.put("data", users);
                result.put("total", total);
                result.put("page", page);
                result.put("size", size);
                result.put("totalPages", (int) Math.ceil((double) total / size));
                result.put("success", true);
            } catch (Exception e2) {
                result.put("data", new ArrayList<>());
                result.put("total", 0);
            }
        }

        return result;
    }

    // 添加删除用户接口
    @DeleteMapping("/deleteUser/{userId}")
    public Map<String, Object> deleteUser(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            userService.deleteUser(userId);
            result.put("success", true);
            result.put("message", "用户删除成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "用户删除失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    /*修改用户信息*/
    @PostMapping("/updateUser")
    public Map<String, Object> updateUser(@RequestBody User user) {
        userService.updateUser(user);
        return Collections.singletonMap("success", true);
    }


}
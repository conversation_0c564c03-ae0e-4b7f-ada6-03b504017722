package cn.gzsf.javawebspringboot.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库完整性检查工具
 */
@Component
public class DatabaseIntegrityChecker {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 检查产品图片数据一致性
     */
    public Map<String, Object> checkProductImageConsistency() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查products表中有image_url但product_images表中没有对应记录的产品
            String sql1 = "SELECT p.id, p.name, p.image_url " +
                         "FROM products p " +
                         "WHERE p.image_url IS NOT NULL AND p.image_url != '' " +
                         "AND NOT EXISTS (SELECT 1 FROM product_images pi WHERE pi.product_id = p.id AND pi.image_url = p.image_url)";
            
            List<Map<String, Object>> inconsistentProducts = jdbcTemplate.queryForList(sql1);
            
            // 检查product_images表中标记为主图但products表中image_url不匹配的记录
            String sql2 = "SELECT pi.product_id, pi.image_url as pi_image_url, p.image_url as p_image_url " +
                         "FROM product_images pi " +
                         "JOIN products p ON pi.product_id = p.id " +
                         "WHERE pi.is_primary = 1 AND pi.image_url != p.image_url";
            
            List<Map<String, Object>> mismatchedPrimary = jdbcTemplate.queryForList(sql2);
            
            // 检查没有主图的产品
            String sql3 = "SELECT p.id, p.name " +
                         "FROM products p " +
                         "WHERE NOT EXISTS (SELECT 1 FROM product_images pi WHERE pi.product_id = p.id AND pi.is_primary = 1)";
            
            List<Map<String, Object>> noMainImage = jdbcTemplate.queryForList(sql3);
            
            result.put("success", true);
            result.put("inconsistentProducts", inconsistentProducts);
            result.put("mismatchedPrimary", mismatchedPrimary);
            result.put("noMainImage", noMainImage);
            result.put("message", "数据一致性检查完成");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 修复产品图片数据一致性
     */
    public Map<String, Object> fixProductImageConsistency() {
        Map<String, Object> result = new HashMap<>();
        int fixedCount = 0;
        
        try {
            // 1. 为没有主图的产品设置第一张图片为主图
            String sql1 = "UPDATE product_images pi1 " +
                         "SET is_primary = 1 " +
                         "WHERE pi1.id = (SELECT min_id FROM (" +
                         "    SELECT MIN(pi2.id) as min_id, pi2.product_id " +
                         "    FROM product_images pi2 " +
                         "    WHERE pi2.product_id NOT IN (" +
                         "        SELECT DISTINCT product_id FROM product_images WHERE is_primary = 1" +
                         "    ) " +
                         "    GROUP BY pi2.product_id" +
                         ") temp WHERE temp.product_id = pi1.product_id)";
            
            int updated1 = jdbcTemplate.update(sql1);
            fixedCount += updated1;
            
            // 2. 同步products表的image_url字段与主图一致
            String sql2 = "UPDATE products p " +
                         "JOIN product_images pi ON p.id = pi.product_id " +
                         "SET p.image_url = pi.image_url " +
                         "WHERE pi.is_primary = 1 AND p.image_url != pi.image_url";
            
            int updated2 = jdbcTemplate.update(sql2);
            fixedCount += updated2;
            
            result.put("success", true);
            result.put("fixedCount", fixedCount);
            result.put("message", "修复完成，共修复 " + fixedCount + " 条记录");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 检查孤立的分类关联记录
     */
    public Map<String, Object> checkOrphanedCategoryRelations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查product_category表中引用不存在产品的记录
            String sql1 = "SELECT pc.* FROM product_category pc " +
                         "LEFT JOIN products p ON pc.product_id = p.id " +
                         "WHERE p.id IS NULL";
            
            List<Map<String, Object>> orphanedProducts = jdbcTemplate.queryForList(sql1);
            
            // 检查product_category表中引用不存在分类的记录
            String sql2 = "SELECT pc.* FROM product_category pc " +
                         "LEFT JOIN categories c ON pc.category_id = c.id " +
                         "WHERE c.id IS NULL";
            
            List<Map<String, Object>> orphanedCategories = jdbcTemplate.queryForList(sql2);
            
            result.put("success", true);
            result.put("orphanedProducts", orphanedProducts);
            result.put("orphanedCategories", orphanedCategories);
            result.put("message", "孤立记录检查完成");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 清理孤立的分类关联记录
     */
    public Map<String, Object> cleanOrphanedCategoryRelations() {
        Map<String, Object> result = new HashMap<>();
        int cleanedCount = 0;
        
        try {
            // 删除引用不存在产品的记录
            String sql1 = "DELETE pc FROM product_category pc " +
                         "LEFT JOIN products p ON pc.product_id = p.id " +
                         "WHERE p.id IS NULL";
            
            int deleted1 = jdbcTemplate.update(sql1);
            cleanedCount += deleted1;
            
            // 删除引用不存在分类的记录
            String sql2 = "DELETE pc FROM product_category pc " +
                         "LEFT JOIN categories c ON pc.category_id = c.id " +
                         "WHERE c.id IS NULL";
            
            int deleted2 = jdbcTemplate.update(sql2);
            cleanedCount += deleted2;
            
            result.put("success", true);
            result.put("cleanedCount", cleanedCount);
            result.put("message", "清理完成，共清理 " + cleanedCount + " 条孤立记录");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 全面的数据库健康检查
     */
    public Map<String, Object> performHealthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> imageCheck = checkProductImageConsistency();
            Map<String, Object> orphanCheck = checkOrphanedCategoryRelations();
            
            result.put("success", true);
            result.put("imageConsistency", imageCheck);
            result.put("orphanedRecords", orphanCheck);
            result.put("message", "数据库健康检查完成");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "健康检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}

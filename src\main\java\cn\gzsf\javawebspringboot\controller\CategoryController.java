package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.Category;
import cn.gzsf.javawebspringboot.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 获取所有分类
    @GetMapping("/all")
    public List<Category> getAllCategories() {
        return categoryService.getAllCategories();
    }

    // 获取前端分类数据（用于index.html）
    @GetMapping("/frontend")
    public Map<String, Object> getFrontendCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Category> categories = categoryService.getAllCategories();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "获取分类数据成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 分页获取分类列表（admin.js需要）
    @GetMapping("/page")
    public Map<String, Object> getCategoriesPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<Category> categories = categoryService.getCategoriesPage(offset, size);
            int total = categoryService.getTotalCategoryCount();

            result.put("success", true);
            result.put("data", categories);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("message", "获取分类列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类列表失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 根据 ID 获取分类
    @GetMapping("/{id}")
    public Category getCategoryById(@PathVariable("id") Long id) {
        return categoryService.getCategoryById(id);
    }

    // 添加分类
    @PostMapping("/add")
    public Map<String, Object> addCategory(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 设置创建时间
            category.setCreatedTime(System.currentTimeMillis());
            category.setUpdatedTime(System.currentTimeMillis());

            boolean success = categoryService.addCategory(category);
            if (success) {
                result.put("success", true);
                result.put("message", "分类添加成功");
            } else {
                result.put("success", false);
                result.put("message", "分类添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "分类添加失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 更新分类
    @PutMapping("/update")
    public Map<String, Object> updateCategory(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 设置更新时间
            category.setUpdatedTime(System.currentTimeMillis());

            boolean success = categoryService.updateCategory(category);
            if (success) {
                result.put("success", true);
                result.put("message", "分类更新成功");
            } else {
                result.put("success", false);
                result.put("message", "分类更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "分类更新失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 删除分类
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteCategory(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = categoryService.deleteCategory(id);
            if (success) {
                result.put("success", true);
                result.put("message", "分类删除成功");
            } else {
                result.put("success", false);
                result.put("message", "分类删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "分类删除失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
}
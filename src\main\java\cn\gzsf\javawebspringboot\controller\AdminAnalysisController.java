package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.service.AdminAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员数据分析控制器
 */
@RestController
@RequestMapping("/api/admin/analysis")
@CrossOrigin(origins = "*")
public class AdminAnalysisController {

    @Autowired
    private AdminAnalysisService adminAnalysisService;

    /**
     * 获取数据分析数据
     */
    @GetMapping("/data")
    public Map<String, Object> getAnalysisData() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> data = adminAnalysisService.getAnalysisData();
            result.put("success", true);
            result.put("data", data);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分析数据失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取销售趋势数据
     */
    @GetMapping("/sales-trend")
    public Map<String, Object> getSalesTrend(@RequestParam(defaultValue = "6") int months) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> data = adminAnalysisService.getSalesTrend(months);
            result.put("success", true);
            result.put("data", data);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取销售趋势失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取产品分类分布数据
     */
    @GetMapping("/category-distribution")
    public Map<String, Object> getCategoryDistribution() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> data = adminAnalysisService.getCategoryDistribution();
            result.put("success", true);
            result.put("data", data);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类分布失败：" + e.getMessage());
        }
        return result;
    }
}

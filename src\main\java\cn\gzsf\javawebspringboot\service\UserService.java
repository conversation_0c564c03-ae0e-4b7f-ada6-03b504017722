package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.User;

import java.util.List;

// 用户服务接口，定义用户相关的业务方法
public interface UserService {
    // 用户登录方法
    User login(String idOrPhone, String password);
    // 用户注册方法
    boolean register(User user);

    List<User> getAllUsers();

    // 新增删除用户方法
    void deleteUser(String userId);

    //编辑用户信息
    boolean updateUser(User user);

    // 分页获取用户列表
    List<User> getUsersPage(int offset, int size);

    // 获取用户总数
    int getTotalUserCount();

    // 根据手机号查找用户
    User findByPhone(String phone);

    // 根据用户ID查找用户
    User findByUserId(String userId);

    // 根据标识符（用户ID或手机号）查找用户
    User findUserByIdentifier(String identifier);

    // 更新用户密码
    boolean updatePassword(String identifier, String newPassword);

}
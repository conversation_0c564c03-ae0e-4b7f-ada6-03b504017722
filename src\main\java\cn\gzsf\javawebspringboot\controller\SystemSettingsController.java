package cn.gzsf.javawebspringboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统设置控制器
 */
@RestController
@RequestMapping("/api/admin/settings")
@CrossOrigin(origins = "*")
public class SystemSettingsController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取系统设置
     */
    @GetMapping
    public Map<String, Object> getSystemSettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建系统设置表（如果不存在）
            createSystemSettingsTableIfNotExists();
            
            // 获取系统设置
            String sql = "SELECT setting_key, setting_value FROM system_settings";
            List<Map<String, Object>> settings = jdbcTemplate.queryForList(sql);
            
            Map<String, Object> settingsMap = new HashMap<>();
            // 设置默认值
            settingsMap.put("siteName", "息壤集");
            settingsMap.put("siteDescription", "天然美妆购物平台");
            settingsMap.put("contactEmail", "<EMAIL>");
            settingsMap.put("themeColor", "#ff6b6b");
            settingsMap.put("emailNotification", true);
            settingsMap.put("orderNotification", true);
            
            // 覆盖数据库中的设置
            for (Map<String, Object> setting : settings) {
                String key = (String) setting.get("setting_key");
                String value = (String) setting.get("setting_value");
                
                // 处理布尔值
                if ("emailNotification".equals(key) || "orderNotification".equals(key)) {
                    settingsMap.put(key, "true".equals(value));
                } else {
                    settingsMap.put(key, value);
                }
            }
            
            result.put("success", true);
            result.put("data", settingsMap);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取系统设置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 保存系统设置
     */
    @PostMapping
    public Map<String, Object> saveSystemSettings(@RequestBody Map<String, Object> settings) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建系统设置表（如果不存在）
            createSystemSettingsTableIfNotExists();
            
            // 保存每个设置项
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue().toString();
                
                // 使用 REPLACE INTO 语句，如果存在则更新，不存在则插入
                String sql = "REPLACE INTO system_settings (setting_key, setting_value, updated_time) VALUES (?, ?, ?)";
                jdbcTemplate.update(sql, key, value, System.currentTimeMillis());
            }
            
            result.put("success", true);
            result.put("message", "系统设置保存成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "保存系统设置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 重置系统设置
     */
    @PostMapping("/reset")
    public Map<String, Object> resetSystemSettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 删除所有设置，让系统使用默认值
            String sql = "DELETE FROM system_settings";
            jdbcTemplate.update(sql);
            
            result.put("success", true);
            result.put("message", "系统设置已重置为默认值");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重置系统设置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 创建系统设置表（如果不存在）
     */
    private void createSystemSettingsTableIfNotExists() {
        String createTableSql = "CREATE TABLE IF NOT EXISTS system_settings (" +
            "id INT AUTO_INCREMENT PRIMARY KEY," +
            "setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键'," +
            "setting_value TEXT COMMENT '设置值'," +
            "created_time BIGINT DEFAULT 0 COMMENT '创建时间'," +
            "updated_time BIGINT DEFAULT 0 COMMENT '更新时间'," +
            "INDEX idx_setting_key (setting_key)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表'";

        jdbcTemplate.execute(createTableSql);
    }
}

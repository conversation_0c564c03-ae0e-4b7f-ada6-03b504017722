package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.util.DatabaseIntegrityChecker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据库维护控制器
 * 提供数据库完整性检查和修复功能
 */
@RestController
@RequestMapping("/admin/database")
public class DatabaseMaintenanceController {

    @Autowired
    private DatabaseIntegrityChecker integrityChecker;

    /**
     * 数据库健康检查
     */
    @GetMapping("/health-check")
    public Map<String, Object> healthCheck() {
        System.out.println("🔍 开始数据库健康检查...");
        Map<String, Object> result = integrityChecker.performHealthCheck();
        System.out.println("✅ 数据库健康检查完成");
        return result;
    }

    /**
     * 检查产品图片数据一致性
     */
    @GetMapping("/check-product-images")
    public Map<String, Object> checkProductImages() {
        System.out.println("🔍 检查产品图片数据一致性...");
        return integrityChecker.checkProductImageConsistency();
    }

    /**
     * 修复产品图片数据一致性
     */
    @PostMapping("/fix-product-images")
    public Map<String, Object> fixProductImages() {
        System.out.println("🔧 修复产品图片数据一致性...");
        Map<String, Object> result = integrityChecker.fixProductImageConsistency();
        System.out.println("✅ 产品图片数据修复完成");
        return result;
    }

    /**
     * 检查孤立的分类关联记录
     */
    @GetMapping("/check-orphaned-relations")
    public Map<String, Object> checkOrphanedRelations() {
        System.out.println("🔍 检查孤立的分类关联记录...");
        return integrityChecker.checkOrphanedCategoryRelations();
    }

    /**
     * 清理孤立的分类关联记录
     */
    @PostMapping("/clean-orphaned-relations")
    public Map<String, Object> cleanOrphanedRelations() {
        System.out.println("🧹 清理孤立的分类关联记录...");
        Map<String, Object> result = integrityChecker.cleanOrphanedCategoryRelations();
        System.out.println("✅ 孤立记录清理完成");
        return result;
    }

    /**
     * 一键修复所有数据问题
     */
    @PostMapping("/auto-fix")
    public Map<String, Object> autoFix() {
        System.out.println("🔧 开始自动修复数据库问题...");
        
        try {
            // 1. 修复产品图片一致性
            Map<String, Object> imageFixResult = integrityChecker.fixProductImageConsistency();
            
            // 2. 清理孤立记录
            Map<String, Object> cleanResult = integrityChecker.cleanOrphanedCategoryRelations();
            
            // 3. 再次检查
            Map<String, Object> finalCheck = integrityChecker.performHealthCheck();
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", true);
            result.put("imageFixResult", imageFixResult);
            result.put("cleanResult", cleanResult);
            result.put("finalCheck", finalCheck);
            result.put("message", "自动修复完成");
            
            System.out.println("✅ 数据库自动修复完成");
            return result;
            
        } catch (Exception e) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "自动修复失败: " + e.getMessage());
            e.printStackTrace();
            return result;
        }
    }
}
